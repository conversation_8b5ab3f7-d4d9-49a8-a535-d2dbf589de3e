package rbac

import (
	"testing"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	rbacv1 "k8s.io/api/rbac/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestConvertBusinessSubjectToK8s(t *testing.T) {
	tests := []struct {
		name        string
		input       rbac.Subject
		expected    rbacv1.Subject
		expectError bool
	}{
		{
			name: "PlatformUser to K8s User",
			input: rbac.Subject{
				Kind: rbac.SubjectKindPlatformUser,
				ID:   "user123",
				Name: "张三",
			},
			expected: rbacv1.Subject{
				Kind:     constants.UserSubjectKind,
				Name:     "rbac.hmc.cn:PlatformUser:user123",
				APIGroup: constants.RBACAPIGroup,
			},
			expectError: false,
		},
		{
			name: "ServiceAccount to K8s ServiceAccount",
			input: rbac.Subject{
				Kind:      rbac.SubjectKindServiceAccount,
				Name:      "my-sa",
				Namespace: "default",
			},
			expected: rbacv1.Subject{
				Kind:      constants.ServiceAccountSubjectKind,
				Name:      "my-sa",
				Namespace: "default",
			},
			expectError: false,
		},
		{
			name: "Tenant to K8s Group",
			input: rbac.Subject{
				Kind: rbac.SubjectKindTenant,
				ID:   "tenant123",
				Name: "租户A",
			},
			expected: rbacv1.Subject{
				Kind:     constants.GroupSubjectKind,
				Name:     "rbac.hmc.cn:Tenant:tenant123",
				APIGroup: constants.RBACAPIGroup,
			},
			expectError: false,
		},
		{
			name: "Invalid Subject Kind",
			input: rbac.Subject{
				Kind: "InvalidKind",
				Name: "test",
			},
			expected:    rbacv1.Subject{},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := convertBusinessSubjectToK8s(tt.input)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}
			
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			
			if result.Kind != tt.expected.Kind {
				t.Errorf("Expected Kind %s, got %s", tt.expected.Kind, result.Kind)
			}
			
			if result.Name != tt.expected.Name {
				t.Errorf("Expected Name %s, got %s", tt.expected.Name, result.Name)
			}
			
			if result.Namespace != tt.expected.Namespace {
				t.Errorf("Expected Namespace %s, got %s", tt.expected.Namespace, result.Namespace)
			}
			
			if result.APIGroup != tt.expected.APIGroup {
				t.Errorf("Expected APIGroup %s, got %s", tt.expected.APIGroup, result.APIGroup)
			}
		})
	}
}

func TestConvertK8sSubjectToBusiness(t *testing.T) {
	tests := []struct {
		name     string
		input    rbacv1.Subject
		expected rbac.Subject
	}{
		{
			name: "K8s User to PlatformUser",
			input: rbacv1.Subject{
				Kind:     constants.UserSubjectKind,
				Name:     "rbac.hmc.cn:account:user123",
				APIGroup: constants.RBACAPIGroup,
			},
			expected: rbac.Subject{
				Kind: rbac.SubjectKindPlatformUser,
				ID:   "user123",
				Name: "user123",
			},
		},
		{
			name: "K8s ServiceAccount to ServiceAccount",
			input: rbacv1.Subject{
				Kind:      constants.ServiceAccountSubjectKind,
				Name:      "my-sa",
				Namespace: "default",
			},
			expected: rbac.Subject{
				Kind:      rbac.SubjectKindServiceAccount,
				Name:      "my-sa",
				Namespace: "default",
			},
		},
		{
			name: "K8s Group to Tenant",
			input: rbacv1.Subject{
				Kind:     constants.GroupSubjectKind,
				Name:     "rbac.hmc.cn:tenant:tenant123",
				APIGroup: constants.RBACAPIGroup,
			},
			expected: rbac.Subject{
				Kind: rbac.SubjectKindTenant,
				ID:   "tenant123",
				Name: "tenant123",
			},
		},
		{
			name: "Native K8s User",
			input: rbacv1.Subject{
				Kind:     constants.UserSubjectKind,
				Name:     "system:admin",
				APIGroup: constants.RBACAPIGroup,
			},
			expected: rbac.Subject{
				Kind: rbac.SubjectKindKubernetesUser,
				Name: "system:admin",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertK8sSubjectToBusiness(tt.input)
			
			if result.Kind != tt.expected.Kind {
				t.Errorf("Expected Kind %s, got %s", tt.expected.Kind, result.Kind)
			}
			
			if result.Name != tt.expected.Name {
				t.Errorf("Expected Name %s, got %s", tt.expected.Name, result.Name)
			}
			
			if result.ID != tt.expected.ID {
				t.Errorf("Expected ID %s, got %s", tt.expected.ID, result.ID)
			}
			
			if result.Namespace != tt.expected.Namespace {
				t.Errorf("Expected Namespace %s, got %s", tt.expected.Namespace, result.Namespace)
			}
		})
	}
}

func TestIsSystemProtectedNamespace(t *testing.T) {
	tests := []struct {
		namespace string
		expected  bool
	}{
		{"kube-system", true},
		{"kube-public", true},
		{"kube-node-lease", true},
		{"default", false},
		{"my-namespace", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(tt.namespace, func(t *testing.T) {
			result := isSystemProtectedNamespace(tt.namespace)
			if result != tt.expected {
				t.Errorf("Expected %v for namespace %s, got %v", tt.expected, tt.namespace, result)
			}
		})
	}
}

func TestIsServiceAccountProtected(t *testing.T) {
	tests := []struct {
		name     string
		sa       *corev1.ServiceAccount
		expected bool
	}{
		{
			name: "Default ServiceAccount in kube-system",
			sa: &corev1.ServiceAccount{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "default",
					Namespace: "kube-system",
				},
			},
			expected: true,
		},
		{
			name: "Default ServiceAccount in default namespace",
			sa: &corev1.ServiceAccount{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "default",
					Namespace: "default",
				},
			},
			expected: true,
		},
		{
			name: "ServiceAccount with protection label",
			sa: &corev1.ServiceAccount{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "my-sa",
					Namespace: "default",
					Labels: map[string]string{
						constants.SystemProtectedLabel: constants.SystemProtectedValue,
					},
				},
			},
			expected: true,
		},
		{
			name: "Regular ServiceAccount",
			sa: &corev1.ServiceAccount{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "my-sa",
					Namespace: "my-namespace",
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isServiceAccountProtected(tt.sa)
			if result != tt.expected {
				t.Errorf("Expected %v for ServiceAccount %s/%s, got %v", 
					tt.expected, tt.sa.Namespace, tt.sa.Name, result)
			}
		})
	}
}

func TestBuildRoleRef(t *testing.T) {
	tests := []struct {
		roleName string
		roleKind string
		expected rbacv1.RoleRef
	}{
		{
			roleName: "my-role",
			roleKind: "Role",
			expected: rbacv1.RoleRef{
				APIGroup: constants.RBACAPIGroup,
				Kind:     "Role",
				Name:     "my-role",
			},
		},
		{
			roleName: "my-cluster-role",
			roleKind: "ClusterRole",
			expected: rbacv1.RoleRef{
				APIGroup: constants.RBACAPIGroup,
				Kind:     "ClusterRole",
				Name:     "my-cluster-role",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.roleName, func(t *testing.T) {
			result := buildRoleRef(tt.roleName, tt.roleKind)
			
			if result.APIGroup != tt.expected.APIGroup {
				t.Errorf("Expected APIGroup %s, got %s", tt.expected.APIGroup, result.APIGroup)
			}
			
			if result.Kind != tt.expected.Kind {
				t.Errorf("Expected Kind %s, got %s", tt.expected.Kind, result.Kind)
			}
			
			if result.Name != tt.expected.Name {
				t.Errorf("Expected Name %s, got %s", tt.expected.Name, result.Name)
			}
		})
	}
}
