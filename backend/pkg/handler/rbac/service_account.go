package rbac

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// CreateServiceAccount 创建服务账号
func (h *handler) CreateServiceAccount(ctx context.Context, req *rbac.CreateServiceAccountRequest) (resp *rbac.CreateServiceAccountResponse, err error) {
	logger.GetLogger().Info("Creating service account",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("serviceAccountName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 构建ServiceAccount对象
	serviceAccount := &corev1.ServiceAccount{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "v1",
			Kind:       constants.ServiceAccountKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:        req.Name,
			Namespace:   req.Namespace,
			Labels:      req.Labels,
			Annotations: req.Annotations,
		},
	}

	// 创建ServiceAccount
	if err := k8sClient.Create(ctx, serviceAccount); err != nil {
		if apierrors.IsAlreadyExists(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACServiceAccountAlreadyExists, req.Name, req.Namespace)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	logger.GetLogger().Info("ServiceAccount created successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("serviceAccountName", req.Name),
	)

	return &rbac.CreateServiceAccountResponse{
		ObjectMeta: serviceAccount.ObjectMeta,
	}, nil
}

// GetServiceAccount 获取服务账号详情
func (h *handler) GetServiceAccount(ctx context.Context, req *rbac.GetServiceAccountRequest) (resp *rbac.GetServiceAccountResponse, err error) {
	logger.GetLogger().Info("Getting service account",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("serviceAccountName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取ServiceAccount
	serviceAccount := &corev1.ServiceAccount{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Namespace: req.Namespace,
		Name:      req.Name,
	}, serviceAccount); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACServiceAccountNotFound, req.Name, req.Namespace)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 转换Secrets
	var secrets []rbac.ServiceAccountSecret
	for _, secret := range serviceAccount.Secrets {
		secrets = append(secrets, rbac.ServiceAccountSecret{
			Name: secret.Name,
		})
	}

	// 检查是否受保护
	protected := isServiceAccountProtected(serviceAccount)

	return &rbac.ServiceAccount{
		TypeMeta:   serviceAccount.TypeMeta,
		ObjectMeta: serviceAccount.ObjectMeta,
		Secrets:    secrets,
		Protected:  protected,
	}, nil
}

// ListServiceAccount 获取服务账号列表
func (h *handler) ListServiceAccount(ctx context.Context, req *rbac.ListServiceAccountRequest) (resp *rbac.ListServiceAccountResponse, err error) {
	logger.GetLogger().Info("Listing service accounts",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取ServiceAccount列表
	serviceAccountList := &corev1.ServiceAccountList{}
	listOptions := []client.ListOption{}

	// 如果指定了命名空间，则只查询该命名空间
	if req.Namespace != "" {
		listOptions = append(listOptions, client.InNamespace(req.Namespace))
	}

	if err := k8sClient.List(ctx, serviceAccountList, listOptions...); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	// 转换为业务模型
	var items []*rbac.ServiceAccount
	for _, sa := range serviceAccountList.Items {
		// 转换Secrets
		var secrets []rbac.ServiceAccountSecret
		for _, secret := range sa.Secrets {
			secrets = append(secrets, rbac.ServiceAccountSecret{
				Name: secret.Name,
			})
		}

		// 检查是否受保护
		protected := isServiceAccountProtected(&sa)

		items = append(items, &rbac.ServiceAccount{
			TypeMeta:   sa.TypeMeta,
			ObjectMeta: sa.ObjectMeta,
			Secrets:    secrets,
			Protected:  protected,
		})
	}

	// 使用Filter过滤器过滤服务账号列表
	result, err := req.Filter.FilterResult(items)
	if err != nil {
		return nil, err
	}

	logger.GetLogger().Info("ServiceAccounts listed successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.Int("total", result.TotalCount),
	)

	return result, nil
}

// UpdateServiceAccount 更新服务账号
func (h *handler) UpdateServiceAccount(ctx context.Context, req *rbac.UpdateServiceAccountRequest) (resp *rbac.UpdateServiceAccountResponse, err error) {
	logger.GetLogger().Info("Updating service account",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("serviceAccountName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取现有ServiceAccount
	existingServiceAccount := &corev1.ServiceAccount{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Namespace: req.Namespace,
		Name:      req.Name,
	}, existingServiceAccount); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACServiceAccountNotFound, req.Name, req.Namespace)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 更新ServiceAccount
	if req.Labels != nil {
		existingServiceAccount.Labels = req.Labels
	}
	if req.Annotations != nil {
		existingServiceAccount.Annotations = req.Annotations
	}

	if err := k8sClient.Update(ctx, existingServiceAccount); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	logger.GetLogger().Info("ServiceAccount updated successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("serviceAccountName", req.Name),
	)

	return &rbac.UpdateServiceAccountResponse{
		ObjectMeta: existingServiceAccount.ObjectMeta,
	}, nil
}

// DeleteServiceAccount 删除服务账号
func (h *handler) DeleteServiceAccount(ctx context.Context, req *rbac.DeleteServiceAccountRequest) (resp *rbac.DeleteServiceAccountResponse, err error) {
	logger.GetLogger().Info("Deleting service account",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("serviceAccountName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取ServiceAccount
	serviceAccount := &corev1.ServiceAccount{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Namespace: req.Namespace,
		Name:      req.Name,
	}, serviceAccount); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACServiceAccountNotFound, req.Name, req.Namespace)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 检查是否受保护
	if isServiceAccountProtected(serviceAccount) {
		return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACServiceAccountProtected, req.Name)
	}

	// 删除ServiceAccount
	if err := k8sClient.Delete(ctx, serviceAccount); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	logger.GetLogger().Info("ServiceAccount deleted successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("serviceAccountName", req.Name),
	)

	return &rbac.DeleteServiceAccountResponse{
		Message: fmt.Sprintf("ServiceAccount '%s' deleted successfully", req.Name),
	}, nil
}

// ValidateServiceAccountName 校验服务账号名称
func (h *handler) ValidateServiceAccountName(ctx context.Context, req *rbac.ValidateServiceAccountNameRequest) (resp *rbac.ValidateServiceAccountNameResponse, err error) {
	logger.GetLogger().Info("Validating service account name",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("serviceAccountName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 检查ServiceAccount是否存在
	serviceAccount := &corev1.ServiceAccount{}
	err = k8sClient.Get(ctx, client.ObjectKey{
		Namespace: req.Namespace,
		Name:      req.Name,
	}, serviceAccount)

	if err != nil {
		if apierrors.IsNotFound(err) {
			// 名称可用
			return &rbac.ValidateServiceAccountNameResponse{
				Valid:  true,
				Reason: fmt.Sprintf("ServiceAccount name '%s' is available", req.Name),
			}, nil
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 名称已存在
	return &rbac.ValidateServiceAccountNameResponse{
		Valid:  false,
		Reason: fmt.Sprintf("ServiceAccount '%s' already exists in namespace '%s'", req.Name, req.Namespace),
	}, nil
}
