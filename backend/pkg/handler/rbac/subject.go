package rbac

import (
	"context"

	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	rbac "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
)

// GetSubjects 获取主体列表
func (h *handler) GetSubjects(ctx context.Context, req *rbac.GetSubjectsRequest) (resp *rbac.GetSubjectsResponse, err error) {
	logger.GetLogger().Info("Getting subjects",
		zap.String("type", req.Type),
	)

	// 构建主体列表
	var subjects []rbac.Subject

	// 根据类型过滤返回不同的主体
	switch req.Type {
	case string(rbac.SubjectKindPlatformUser):
		// 返回平台用户列表（这里应该从实际的用户服务获取）
		subjects = append(subjects, rbac.Subject{
			Kind: rbac.SubjectKindPlatformUser,
			ID:   "user001",
			Name: "张三",
		}, rbac.Subject{
			Kind: rbac.SubjectKindPlatformUser,
			ID:   "user002",
			Name: "李四",
		})
	case string(rbac.SubjectKindPlatformRole):
		// 返回平台角色列表
		subjects = append(subjects, rbac.Subject{
			Kind: rbac.SubjectKindPlatformRole,
			ID:   "admin",
			Name: "管理员",
		}, rbac.Subject{
			Kind: rbac.SubjectKindPlatformRole,
			ID:   "developer",
			Name: "开发者",
		})
	case string(rbac.SubjectKindTenant):
		// 返回租户列表
		subjects = append(subjects, rbac.Subject{
			Kind: rbac.SubjectKindTenant,
			ID:   "tenant001",
			Name: "财务部",
		}, rbac.Subject{
			Kind: rbac.SubjectKindTenant,
			ID:   "tenant002",
			Name: "技术部",
		})
	case string(rbac.SubjectKindProject):
		// 返回项目列表
		subjects = append(subjects, rbac.Subject{
			Kind: rbac.SubjectKindProject,
			ID:   "project001",
			Name: "项目A",
		}, rbac.Subject{
			Kind: rbac.SubjectKindProject,
			ID:   "project002",
			Name: "项目B",
		})
	default:
		// 返回所有类型的主体
		subjects = append(subjects,
			// 平台用户
			rbac.Subject{
				Kind: rbac.SubjectKindPlatformUser,
				ID:   "user001",
				Name: "张三",
			},
			rbac.Subject{
				Kind: rbac.SubjectKindPlatformUser,
				ID:   "user002",
				Name: "李四",
			},
			// 平台角色
			rbac.Subject{
				Kind: rbac.SubjectKindPlatformRole,
				ID:   "admin",
				Name: "管理员",
			},
			rbac.Subject{
				Kind: rbac.SubjectKindPlatformRole,
				ID:   "developer",
				Name: "开发者",
			},
			// 租户
			rbac.Subject{
				Kind: rbac.SubjectKindTenant,
				ID:   "tenant001",
				Name: "财务部",
			},
			rbac.Subject{
				Kind: rbac.SubjectKindTenant,
				ID:   "tenant002",
				Name: "技术部",
			},
			// 项目
			rbac.Subject{
				Kind: rbac.SubjectKindProject,
				ID:   "project001",
				Name: "项目A",
			},
			rbac.Subject{
				Kind: rbac.SubjectKindProject,
				ID:   "project002",
				Name: "项目B",
			},
		)
	}

	resp = &rbac.GetSubjectsResponse{
		Subjects: subjects,
	}

	logger.GetLogger().Info("Subjects retrieved successfully",
		zap.String("type", req.Type),
		zap.Int("count", len(subjects)),
	)

	return resp, nil
}
