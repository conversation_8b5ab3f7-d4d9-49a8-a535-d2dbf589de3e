package rbac

import (
	"context"
	"fmt"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	"go.uber.org/zap"
	rbacv1 "k8s.io/api/rbac/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// CreateClusterRole 创建集群角色
func (h *handler) CreateClusterRole(ctx context.Context, req *rbac.CreateClusterRoleRequest) (resp *rbac.CreateClusterRoleResponse, err error) {
	logger.GetLogger().Info("Creating cluster role",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleName", req.Name),
	)

	// 验证策略规则
	if err := validatePolicyRules(req.Rules); err != nil {
		return nil, err
	}

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 构建ClusterRole对象
	clusterRole := &rbacv1.ClusterRole{
		TypeMeta: metav1.TypeMeta{
			APIVersion: constants.RBACAPIGroup + "/" + constants.RBACAPIVersion,
			Kind:       constants.ClusterRoleKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:   req.Name,
			Labels: req.Labels,
		},
		Rules: req.Rules,
	}

	// 创建ClusterRole
	if err := k8sClient.Create(ctx, clusterRole); err != nil {
		if apierrors.IsAlreadyExists(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACClusterRoleAlreadyExists, req.Name)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	logger.GetLogger().Info("ClusterRole created successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleName", req.Name),
	)

	return &rbac.CreateClusterRoleResponse{
		ObjectMeta: clusterRole.ObjectMeta,
		Rules:      clusterRole.Rules,
	}, nil
}

// GetClusterRole 获取集群角色详情
func (h *handler) GetClusterRole(ctx context.Context, req *rbac.GetClusterRoleRequest) (resp *rbac.GetClusterRoleResponse, err error) {
	logger.GetLogger().Info("Getting cluster role",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取ClusterRole
	clusterRole := &rbacv1.ClusterRole{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Name: req.Name,
	}, clusterRole); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACClusterRoleNotFound, req.Name)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	return &rbac.ClusterRole{
		TypeMeta:        clusterRole.TypeMeta,
		ObjectMeta:      clusterRole.ObjectMeta,
		Rules:           clusterRole.Rules,
		AggregationRule: clusterRole.AggregationRule,
	}, nil
}

// ListClusterRole 获取集群角色列表
func (h *handler) ListClusterRole(ctx context.Context, req *rbac.ListClusterRoleRequest) (resp *rbac.ListClusterRoleResponse, err error) {
	logger.GetLogger().Info("Listing cluster roles",
		zap.String("cluster", req.ClusterID),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取ClusterRole列表
	clusterRoleList := &rbacv1.ClusterRoleList{}
	if err := k8sClient.List(ctx, clusterRoleList); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	// 转换为业务模型
	var items []*rbac.ClusterRole
	for _, clusterRole := range clusterRoleList.Items {
		items = append(items, &rbac.ClusterRole{
			TypeMeta:        clusterRole.TypeMeta,
			ObjectMeta:      clusterRole.ObjectMeta,
			Rules:           clusterRole.Rules,
			AggregationRule: clusterRole.AggregationRule,
		})
	}

	// 使用Filter过滤器过滤集群角色列表
	result, err := req.Filter.Filter(items)
	if err != nil {
		return nil, err
	}

	logger.GetLogger().Info("ClusterRoles listed successfully",
		zap.String("cluster", req.ClusterID),
		zap.Int("total", result.Total),
	)

	return result, nil
}

// UpdateClusterRole 更新集群角色
func (h *handler) UpdateClusterRole(ctx context.Context, req *rbac.UpdateClusterRoleRequest) (resp *rbac.UpdateClusterRoleResponse, err error) {
	logger.GetLogger().Info("Updating cluster role",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleName", req.Name),
	)

	// 验证策略规则
	if err := validatePolicyRules(req.ClusterRole.Rules); err != nil {
		return nil, err
	}

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取现有ClusterRole
	existingClusterRole := &rbacv1.ClusterRole{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Name: req.Name,
	}, existingClusterRole); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACClusterRoleNotFound, req.Name)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 更新ClusterRole
	existingClusterRole.Rules = req.ClusterRole.Rules
	if req.ClusterRole.Labels != nil {
		existingClusterRole.Labels = req.ClusterRole.Labels
	}
	if req.ClusterRole.Annotations != nil {
		existingClusterRole.Annotations = req.ClusterRole.Annotations
	}
	if req.ClusterRole.AggregationRule != nil {
		existingClusterRole.AggregationRule = req.ClusterRole.AggregationRule
	}

	if err := k8sClient.Update(ctx, existingClusterRole); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	logger.GetLogger().Info("ClusterRole updated successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleName", req.Name),
	)

	return &rbac.UpdateClusterRoleResponse{
		Name: req.Name,
	}, nil
}

// DeleteClusterRole 删除集群角色
func (h *handler) DeleteClusterRole(ctx context.Context, req *rbac.DeleteClusterRoleRequest) (resp *rbac.DeleteClusterRoleResponse, err error) {
	logger.GetLogger().Info("Deleting cluster role",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 检查ClusterRole是否被使用
	if err := checkClusterRoleInUse(ctx, k8sClient, req.Name); err != nil {
		return nil, err
	}

	// 获取ClusterRole
	clusterRole := &rbacv1.ClusterRole{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Name: req.Name,
	}, clusterRole); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACClusterRoleNotFound, req.Name)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 删除ClusterRole
	if err := k8sClient.Delete(ctx, clusterRole); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	logger.GetLogger().Info("ClusterRole deleted successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleName", req.Name),
	)

	return &rbac.DeleteClusterRoleResponse{
		Message: fmt.Sprintf("ClusterRole '%s' deleted successfully", req.Name),
	}, nil
}

// ValidateClusterRoleName 校验集群角色名称
func (h *handler) ValidateClusterRoleName(ctx context.Context, req *rbac.ValidateClusterRoleNameRequest) (resp *rbac.ValidateClusterRoleNameResponse, err error) {
	logger.GetLogger().Info("Validating cluster role name",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 检查ClusterRole是否存在
	clusterRole := &rbacv1.ClusterRole{}
	err = k8sClient.Get(ctx, client.ObjectKey{
		Name: req.Name,
	}, clusterRole)

	if err != nil {
		if apierrors.IsNotFound(err) {
			// 名称可用
			return &rbac.ValidateClusterRoleNameResponse{
				Message: fmt.Sprintf("ClusterRole name '%s' is available", req.Name),
			}, nil
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 名称已存在
	return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACClusterRoleAlreadyExists, req.Name)
}
