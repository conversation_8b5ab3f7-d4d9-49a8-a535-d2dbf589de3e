package rbac

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
)

// RoleHandler 角色处理器接口
type RoleHandler interface {
	// CreateRole 创建角色
	CreateRole(ctx context.Context, req *rbac.CreateRoleRequest) (resp *rbac.CreateRoleResponse, err error)
	// GetRole 获取角色详情
	GetRole(ctx context.Context, req *rbac.GetRoleRequest) (resp *rbac.GetRoleResponse, err error)
	// ListRole 获取角色列表
	ListRole(ctx context.Context, req *rbac.ListRoleRequest) (resp *rbac.ListRoleResponse, err error)
	// UpdateRole 更新角色
	UpdateRole(ctx context.Context, req *rbac.UpdateRoleRequest) (resp *rbac.UpdateRoleResponse, err error)
	// DeleteRole 删除角色
	DeleteRole(ctx context.Context, req *rbac.DeleteRoleRequest) (resp *rbac.DeleteRoleResponse, err error)
	// ValidateRoleName 校验角色名称
	ValidateRoleName(ctx context.Context, req *rbac.ValidateRoleNameRequest) (resp *rbac.ValidateRoleNameResponse, err error)
}

// ClusterRoleHandler 集群角色处理器接口
type ClusterRoleHandler interface {
	// CreateClusterRole 创建集群角色
	CreateClusterRole(ctx context.Context, req *rbac.CreateClusterRoleRequest) (resp *rbac.CreateClusterRoleResponse, err error)
	// GetClusterRole 获取集群角色详情
	GetClusterRole(ctx context.Context, req *rbac.GetClusterRoleRequest) (resp *rbac.GetClusterRoleResponse, err error)
	// ListClusterRole 获取集群角色列表
	ListClusterRole(ctx context.Context, req *rbac.ListClusterRoleRequest) (resp *rbac.ListClusterRoleResponse, err error)
	// UpdateClusterRole 更新集群角色
	UpdateClusterRole(ctx context.Context, req *rbac.UpdateClusterRoleRequest) (resp *rbac.UpdateClusterRoleResponse, err error)
	// DeleteClusterRole 删除集群角色
	DeleteClusterRole(ctx context.Context, req *rbac.DeleteClusterRoleRequest) (resp *rbac.DeleteClusterRoleResponse, err error)
	// ValidateClusterRoleName 校验集群角色名称
	ValidateClusterRoleName(ctx context.Context, req *rbac.ValidateClusterRoleNameRequest) (resp *rbac.ValidateClusterRoleNameResponse, err error)
}

// RoleBindingHandler 角色绑定处理器接口
type RoleBindingHandler interface {
	// CreateRoleBinding 创建角色绑定
	CreateRoleBinding(ctx context.Context, req *rbac.CreateRoleBindingRequest) (resp *rbac.CreateRoleBindingResponse, err error)
	// GetRoleBinding 获取角色绑定详情
	GetRoleBinding(ctx context.Context, req *rbac.GetRoleBindingRequest) (resp *rbac.GetRoleBindingResponse, err error)
	// ListRoleBinding 获取角色绑定列表
	ListRoleBinding(ctx context.Context, req *rbac.ListRoleBindingRequest) (resp *rbac.ListRoleBindingResponse, err error)
	// UpdateRoleBinding 更新角色绑定
	UpdateRoleBinding(ctx context.Context, req *rbac.UpdateRoleBindingRequest) (resp *rbac.UpdateRoleBindingResponse, err error)
	// DeleteRoleBinding 删除角色绑定
	DeleteRoleBinding(ctx context.Context, req *rbac.DeleteRoleBindingRequest) (resp *rbac.DeleteRoleBindingResponse, err error)
}

// ClusterRoleBindingHandler 集群角色绑定处理器接口
type ClusterRoleBindingHandler interface {
	// CreateClusterRoleBinding 创建集群角色绑定
	CreateClusterRoleBinding(ctx context.Context, req *rbac.CreateClusterRoleBindingRequest) (resp *rbac.CreateClusterRoleBindingResponse, err error)
	// GetClusterRoleBinding 获取集群角色绑定详情
	GetClusterRoleBinding(ctx context.Context, req *rbac.GetClusterRoleBindingRequest) (resp *rbac.GetClusterRoleBindingResponse, err error)
	// ListClusterRoleBinding 获取集群角色绑定列表
	ListClusterRoleBinding(ctx context.Context, req *rbac.ListClusterRoleBindingRequest) (resp *rbac.ListClusterRoleBindingResponse, err error)
	// UpdateClusterRoleBinding 更新集群角色绑定
	UpdateClusterRoleBinding(ctx context.Context, req *rbac.UpdateClusterRoleBindingRequest) (resp *rbac.UpdateClusterRoleBindingResponse, err error)
	// DeleteClusterRoleBinding 删除集群角色绑定
	DeleteClusterRoleBinding(ctx context.Context, req *rbac.DeleteClusterRoleBindingRequest) (resp *rbac.DeleteClusterRoleBindingResponse, err error)
	// ValidateClusterRoleBindingName 校验集群角色绑定名称
	ValidateClusterRoleBindingName(ctx context.Context, req *rbac.ValidateClusterRoleBindingNameRequest) (resp *rbac.ValidateClusterRoleBindingNameResponse, err error)
}

// ServiceAccountHandler 服务账号处理器接口
type ServiceAccountHandler interface {
	// CreateServiceAccount 创建服务账号
	CreateServiceAccount(ctx context.Context, req *rbac.CreateServiceAccountRequest) (resp *rbac.CreateServiceAccountResponse, err error)
	// GetServiceAccount 获取服务账号详情
	GetServiceAccount(ctx context.Context, req *rbac.GetServiceAccountRequest) (resp *rbac.GetServiceAccountResponse, err error)
	// ListServiceAccount 获取服务账号列表
	ListServiceAccount(ctx context.Context, req *rbac.ListServiceAccountRequest) (resp *rbac.ListServiceAccountResponse, err error)
	// UpdateServiceAccount 更新服务账号
	UpdateServiceAccount(ctx context.Context, req *rbac.UpdateServiceAccountRequest) (resp *rbac.UpdateServiceAccountResponse, err error)
	// DeleteServiceAccount 删除服务账号
	DeleteServiceAccount(ctx context.Context, req *rbac.DeleteServiceAccountRequest) (resp *rbac.DeleteServiceAccountResponse, err error)
	// ValidateServiceAccountName 校验服务账号名称
	ValidateServiceAccountName(ctx context.Context, req *rbac.ValidateServiceAccountNameRequest) (resp *rbac.ValidateServiceAccountNameResponse, err error)
}

// APIResourceHandler API资源发现处理器接口
type APIResourceHandler interface {
	// GetAPIResources 获取集群可用API资源
	GetAPIResources(ctx context.Context, req *rbac.GetAPIResourcesRequest) (resp *rbac.GetAPIResourcesResponse, err error)
}

// WorkspaceRoleHandler 工作空间角色处理器接口
type WorkspaceRoleHandler interface {
	// ListWorkspaceRole 获取工作空间角色列表
	ListWorkspaceRole(ctx context.Context, req *rbac.WorkspaceListRoleRequest) (resp *rbac.ListRoleResponse, err error)
	// GetWorkspaceRole 获取工作空间角色详情
	GetWorkspaceRole(ctx context.Context, req *rbac.WorkspaceGetRoleRequest) (resp *rbac.GetRoleResponse, err error)
	// CreateWorkspaceRole 创建工作空间角色
	CreateWorkspaceRole(ctx context.Context, req *rbac.WorkspaceCreateRoleRequest) (resp *rbac.CreateRoleResponse, err error)
	// UpdateWorkspaceRole 更新工作空间角色
	UpdateWorkspaceRole(ctx context.Context, req *rbac.WorkspaceUpdateRoleRequest) (resp *rbac.UpdateRoleResponse, err error)
	// DeleteWorkspaceRole 删除工作空间角色
	DeleteWorkspaceRole(ctx context.Context, req *rbac.WorkspaceDeleteRoleRequest) (resp *rbac.DeleteRoleResponse, err error)
	// GetBindableRoles 获取可绑定角色列表
	GetBindableRoles(ctx context.Context, req *rbac.GetBindableRolesRequest) (resp *rbac.GetBindableRolesResponse, err error)
}

// SubjectHandler 主体处理器接口
type SubjectHandler interface {
	// GetSubjects 获取主体列表
	GetSubjects(ctx context.Context, req *rbac.GetSubjectsRequest) (resp *rbac.GetSubjectsResponse, err error)
}

// Handler RBAC模块总处理器接口
type Handler interface {
	RoleHandler
	ClusterRoleHandler
	RoleBindingHandler
	ClusterRoleBindingHandler
	ServiceAccountHandler
	APIResourceHandler
	WorkspaceRoleHandler
	SubjectHandler
}

// NewHandler 创建RBAC处理器实例
func NewHandler() Handler {
	return &handler{}
}

// handler RBAC处理器实现
type handler struct{}

// 确保handler实现了Handler接口
var _ Handler = (*handler)(nil)
