package rbac

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	rbacModels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

// ===== ServiceAccount 相关路由处理器 =====

// CreateServiceAccount 创建服务账号
// @Summary 创建服务账号
// @Description 在指定集群和命名空间中创建服务账号
// @Tags RBAC-ServiceAccount
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param body body rbacModels.CreateServiceAccountRequest true "创建服务账号请求"
// @Success 200 {object} rbacModels.CreateServiceAccountResponse
// @Failure 400 {object} router.ErrorResponse
// @Failure 409 {object} router.ErrorResponse "服务账号已存在"
// @Router /api/v1/clusters/{clusterId}/namespaces/{namespace}/rbac/service-accounts [post]
func (r *ClusterSpaceRouter) CreateServiceAccount(c *gin.Context) {
	var req rbacModels.CreateServiceAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	// 从路径参数获取集群ID和命名空间
	req.ClusterID = c.Query("clusterId")
	req.Namespace = c.Query("namespace")

	resp, err := r.handler.CreateServiceAccount(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// GetServiceAccount 获取服务账号详情
// @Summary 获取服务账号详情
// @Description 获取指定服务账号的详细信息
// @Tags RBAC-ServiceAccount
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param name path string true "服务账号名称"
// @Success 200 {object} rbacModels.GetServiceAccountResponse
// @Failure 404 {object} router.ErrorResponse "服务账号不存在"
// @Router /api/v1/clusters/{clusterId}/namespaces/{namespace}/rbac/service-accounts/{name} [get]
func (r *ClusterSpaceRouter) GetServiceAccount(c *gin.Context) {
	req := rbacModels.GetServiceAccountRequest{
		ClusterID: c.Query("clusterId"),
		Namespace: c.Query("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.GetServiceAccount(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ListServiceAccount 获取服务账号列表
// @Summary 获取服务账号列表
// @Description 获取指定集群和命名空间的服务账号列表
// @Tags RBAC-ServiceAccount
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param namespace query string false "命名空间"
// @Success 200 {object} rbacModels.ListServiceAccountResponse
// @Router /api/v1/clusters/{clusterId}/rbac/service-accounts [get]
func (r *ClusterSpaceRouter) ListServiceAccount(c *gin.Context) {
	req := rbacModels.ListServiceAccountRequest{
		ClusterID: c.Query("clusterId"),
		Namespace: c.Query("namespace"),
	}

	// 设置过滤器
	req.Filter = utils.ParseQueryParams[*rbacModels.ServiceAccount](c)

	resp, err := r.handler.ListServiceAccount(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// UpdateServiceAccount 更新服务账号
// @Summary 更新服务账号
// @Description 更新指定服务账号的信息
// @Tags RBAC-ServiceAccount
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param name path string true "服务账号名称"
// @Param body body rbacModels.UpdateServiceAccountRequest true "更新服务账号请求"
// @Success 200 {object} rbacModels.UpdateServiceAccountResponse
// @Failure 404 {object} router.ErrorResponse "服务账号不存在"
// @Router /api/v1/clusters/{clusterId}/namespaces/{namespace}/rbac/service-accounts/{name} [put]
func (r *ClusterSpaceRouter) UpdateServiceAccount(c *gin.Context) {
	var req rbacModels.UpdateServiceAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	req.ClusterID = c.Query("clusterId")
	req.Namespace = c.Query("namespace")
	req.Name = c.Param("name")

	resp, err := r.handler.UpdateServiceAccount(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// DeleteServiceAccount 删除服务账号
// @Summary 删除服务账号
// @Description 删除指定的服务账号
// @Tags RBAC-ServiceAccount
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param name path string true "服务账号名称"
// @Success 200 {object} rbacModels.DeleteServiceAccountResponse
// @Failure 404 {object} router.ErrorResponse "服务账号不存在"
// @Failure 403 {object} router.ErrorResponse "服务账号受保护"
// @Router /api/v1/clusters/{clusterId}/namespaces/{namespace}/rbac/service-accounts/{name} [delete]
func (r *ClusterSpaceRouter) DeleteServiceAccount(c *gin.Context) {
	req := rbacModels.DeleteServiceAccountRequest{
		ClusterID: c.Query("clusterId"),
		Namespace: c.Query("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.DeleteServiceAccount(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ValidateServiceAccountName 校验服务账号名称
// @Summary 校验服务账号名称
// @Description 校验服务账号名称是否可用
// @Tags RBAC-ServiceAccount
// @Accept json
// @Produce json
// @Param body body rbacModels.ValidateServiceAccountNameRequest true "校验请求"
// @Success 200 {object} rbacModels.ValidateServiceAccountNameResponse
// @Router /api/v1/rbac/service-accounts/validate-name [post]
func (r *ClusterSpaceRouter) ValidateServiceAccountName(c *gin.Context) {
	var req rbacModels.ValidateServiceAccountNameRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	resp, err := r.handler.ValidateServiceAccountName(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ===== API资源发现 相关路由处理器 =====

// GetAPIResources 获取集群可用API资源
// @Summary 获取集群可用API资源
// @Description 获取指定集群的可用API资源列表，用于权限配置
// @Tags RBAC-APIResource
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param apiGroup query string false "API组过滤"
// @Param keyword query string false "关键字过滤"
// @Success 200 {object} rbacModels.GetAPIResourcesResponse
// @Router /api/v1/clusters/{clusterId}/rbac/api-resources [get]
func (r *ClusterSpaceRouter) GetAPIResources(c *gin.Context) {
	req := rbacModels.GetAPIResourcesRequest{
		ClusterID: c.Query("clusterId"),
		APIGroup:  c.Query("apiGroup"),
		Keyword:   c.Query("keyword"),
	}

	resp, err := r.handler.GetAPIResources(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}
