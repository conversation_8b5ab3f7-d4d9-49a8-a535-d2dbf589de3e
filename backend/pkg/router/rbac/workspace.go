package rbac

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/rbac"
	rbacModels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

// WorkspaceRouter 工作空间RBAC路由
type WorkspaceRouter struct {
	handler rbac.Handler
}

// NewWorkspaceRouter 创建工作空间RBAC路由
func NewWorkspaceRouter() *WorkspaceRouter {
	return &WorkspaceRouter{
		handler: rbac.NewHandler(),
	}
}

// RegisterRoutes 注册工作空间RBAC路由
func (r *WorkspaceRouter) RegisterRoutes(rg *gin.RouterGroup) {
	// 工作空间Role相关路由
	roleGroup := rg.Group("/roles")
	{
		roleGroup.GET("", r.ListWorkspaceRole)
		roleGroup.GET("/:name", r.GetWorkspaceRole)
		roleGroup.POST("", r.CreateWorkspaceRole)
		roleGroup.PUT("/:name", r.UpdateWorkspaceRole)
		roleGroup.DELETE("/:name", r.DeleteWorkspaceRole)
	}
}

// ===== 工作空间Role 相关路由处理器 =====

// ListWorkspaceRole 获取工作空间角色列表
// @Summary 获取工作空间角色列表
// @Description 获取指定工作空间的角色列表
// @Tags RBAC-WorkspaceRole
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId query string true "集群ID"
// @Param namespace query string false "命名空间"
// @Success 200 {object} rbacModels.ListRoleResponse
// @Router /api/v1/organizations/{organizationId}/projects/{projectId}/rbac/roles [get]
func (r *WorkspaceRouter) ListWorkspaceRole(c *gin.Context) {
	req := rbacModels.WorkspaceListRoleRequest{
		WorkspaceRoleRequest: rbacModels.WorkspaceRoleRequest{
			OrganizationID: c.Param("organizationId"),
			ProjectID:      c.Param("projectId"),
			ClusterID:      c.Query("clusterId"),
		},
		Namespace: c.Query("namespace"),
	}

	// 设置过滤器
	req.Filter = utils.ParseQueryParams[*rbacModels.Role](c)

	resp, err := r.handler.ListWorkspaceRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// GetWorkspaceRole 获取工作空间角色详情
// @Summary 获取工作空间角色详情
// @Description 获取指定工作空间角色的详细信息
// @Tags RBAC-WorkspaceRole
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param name path string true "角色名称"
// @Success 200 {object} rbacModels.GetRoleResponse
// @Failure 404 {object} router.ErrorResponse "角色不存在"
// @Router /api/v1/organizations/{organizationId}/projects/{projectId}/rbac/roles/{name} [get]
func (r *WorkspaceRouter) GetWorkspaceRole(c *gin.Context) {
	req := rbacModels.WorkspaceGetRoleRequest{
		WorkspaceRoleRequest: rbacModels.WorkspaceRoleRequest{
			OrganizationID: c.Param("organizationId"),
			ProjectID:      c.Param("projectId"),
			ClusterID:      c.Query("clusterId"),
		},
		Namespace: c.Query("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.GetWorkspaceRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// CreateWorkspaceRole 创建工作空间角色
// @Summary 创建工作空间角色
// @Description 在指定工作空间中创建角色
// @Tags RBAC-WorkspaceRole
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param body body rbacModels.WorkspaceCreateRoleRequest true "创建角色请求"
// @Success 200 {object} rbacModels.CreateRoleResponse
// @Failure 400 {object} router.ErrorResponse
// @Failure 409 {object} router.ErrorResponse "角色已存在"
// @Router /api/v1/organizations/{organizationId}/projects/{projectId}/rbac/roles [post]
func (r *WorkspaceRouter) CreateWorkspaceRole(c *gin.Context) {
	var req rbacModels.WorkspaceCreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	// 从路径参数获取工作空间信息
	req.OrganizationID = c.Param("organizationId")
	req.ProjectID = c.Param("projectId")
	req.ClusterID = c.Query("clusterId")
	req.Namespace = c.Query("namespace")

	resp, err := r.handler.CreateWorkspaceRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// UpdateWorkspaceRole 更新工作空间角色
// @Summary 更新工作空间角色
// @Description 更新指定工作空间角色的信息
// @Tags RBAC-WorkspaceRole
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param name path string true "角色名称"
// @Param body body rbacModels.Role true "角色信息"
// @Success 200 {object} rbacModels.UpdateRoleResponse
// @Failure 404 {object} router.ErrorResponse "角色不存在"
// @Router /api/v1/organizations/{organizationId}/projects/{projectId}/rbac/roles/{name} [put]
func (r *WorkspaceRouter) UpdateWorkspaceRole(c *gin.Context) {
	var role rbacModels.Role
	if err := c.ShouldBindJSON(&role); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	req := rbacModels.WorkspaceUpdateRoleRequest{
		WorkspaceRoleRequest: rbacModels.WorkspaceRoleRequest{
			OrganizationID: c.Param("organizationId"),
			ProjectID:      c.Param("projectId"),
			ClusterID:      c.Query("clusterId"),
		},
		Namespace: c.Query("namespace"),
		Name:      c.Param("name"),
		Role:      &role,
	}

	resp, err := r.handler.UpdateWorkspaceRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// DeleteWorkspaceRole 删除工作空间角色
// @Summary 删除工作空间角色
// @Description 删除指定的工作空间角色
// @Tags RBAC-WorkspaceRole
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param name path string true "角色名称"
// @Success 200 {object} rbacModels.DeleteRoleResponse
// @Failure 404 {object} router.ErrorResponse "角色不存在"
// @Failure 409 {object} router.ErrorResponse "角色正在被使用"
// @Router /api/v1/organizations/{organizationId}/projects/{projectId}/rbac/roles/{name} [delete]
func (r *WorkspaceRouter) DeleteWorkspaceRole(c *gin.Context) {
	req := rbacModels.WorkspaceDeleteRoleRequest{
		WorkspaceRoleRequest: rbacModels.WorkspaceRoleRequest{
			OrganizationID: c.Param("organizationId"),
			ProjectID:      c.Param("projectId"),
			ClusterID:      c.Query("clusterId"),
		},
		Namespace: c.Query("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.DeleteWorkspaceRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}
