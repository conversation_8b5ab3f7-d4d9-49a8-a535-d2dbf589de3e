# API 设计文档

本文档遵循 `TASK.md` 中定义的API设计规范。

---

## 第一部分：集群空间API

### 1. 角色管理 (Role)

#### 1.1 获取角色列表

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/roles`

   获取指定集群内的角色列表。可以按命名空间进行过滤。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `namespace` | string | 否 | 命名空间名称。如果未提供，则查询所有命名空间下的角色。 |
    | `selector` | string | 否 | 符合查询参数的选择器。例如：`name=a,namespace=default`；模糊查询 `name~a`。 |
    | `page_num` | integer | 否 | 页码, 默认为1。如果 `page_num` 和 `page_size` 均不提供，则返回所有对象。 |
    | `page_size` | integer | 否 | 每页大小, 默认为10。 |
    | `sort_func` | string | 否 | 排序字段比较方式，例如 `time`, `string`, `number`。 |
    | `sort_name` | string | 否 | 排序字段，通过类似 JSONPath 的方式获取, 默认为 `.metadata.name`。 |
    | `sort_order` | string | 否 | 排序方式 `asc` 或 `desc`，默认 `desc`。 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "total": 1,
            "items": [
                {
                    "metadata": {
                        "name": "my-role",
                        "namespace": "default",
                        "uid": "a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6",
                        "creationTimestamp": "2025-06-12T10:00:00Z"
                    },
                    "rules": [
                        {
                            "verbs": ["get", "list", "watch"],
                            "apiGroups": [""],
                            "resources": ["pods"]
                        }
                    ]
                }
            ]
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/roles?namespace=default&selector=name~my&page_num=1&page_size=10&sort_name=.metadata.creationTimestamp&sort_order=asc' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 1.2 获取角色详情

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/roles/{name}`

   获取特定命名空间下单个角色的详细信息。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色所在的命名空间 |
    | `name` | string | 是 | 角色的名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": {
                "name": "my-role",
                "namespace": "default",
                "uid": "a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6",
                "creationTimestamp": "2025-06-12T10:00:00Z",
                "labels": {
                    "app": "my-app"
                }
            },
            "rules": [
                {
                    "verbs": ["get", "list", "watch"],
                    "apiGroups": [""],
                    "resources": ["pods"]
                },
                {
                    "verbs": ["create", "delete"],
                    "apiGroups": ["apps"],
                    "resources": ["deployments"]
                }
            ]
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/namespaces/default/roles/my-role' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 1.3 创建角色 (表单方式)

**请求**
   `POST /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/roles`

   在指定命名空间中创建一个新角色。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色将要创建于的命名空间 |

*   **Body 参数**
    ```json
    {
        "name": "new-role",
        "labels": {
            "creator": "admin"
        },
        "rules": [
            {
                "verbs": ["get", "list"],
                "apiGroups": [""],
                "resources": ["services", "endpoints"]
            }
        ]
    }
    ```

**出参**

*   **成功响应 (201 Created)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": {
                "name": "new-role",
                "namespace": "default",
                "creationTimestamp": "2025-06-12T11:00:00Z"
            },
            "rules": [
                {
                    "verbs": ["get", "list"],
                    "apiGroups": [""],
                    "resources": ["services", "endpoints"]
                }
            ]
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/namespaces/default/roles' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "new-role",
        "labels": { "creator": "admin" },
        "rules": [
            { "verbs": ["get", "list"], "apiGroups": [""], "resources": ["services", "endpoints"] }
        ]
    }'
    ```

---

#### 1.4 通过YAML操作角色

**说明**

根据要求，通过YAML创建、更新或校验资源时，应直接调用平台提供的K8s代理接口。

*   **创建**: 使用 `POST` 方法。
*   **更新**: 使用 `PUT` 方法。
*   **校验**: 在创建或更新请求的URL后附加 `?dryRun=All` 参数。API服务器会执行除持久化外的所有检查。

**请求**

*   **创建**: `POST /olympus-portal/k8s/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/namespaces/{namespace}/roles`
*   **更新**: `PUT /olympus-portal/k8s/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/namespaces/{namespace}/roles/{name}`
*   **校验**: `POST /olympus-portal/k8s/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/namespaces/{namespace}/roles?dryRun=All`

**入参**

*   **Body 参数**: 完整的 Kubernetes Role 资源对象 (JSON/YAML格式)。

**示例**

*   **Curl 调用 (校验YAML)**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/k8s/clusters/my-cluster/apis/rbac.authorization.k8s.io/v1/namespaces/default/roles?dryRun=All' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/yaml' \
    --data-raw '
    apiVersion: rbac.authorization.k8s.io/v1
    kind: Role
    metadata:
      name: yaml-validated-role
      namespace: default
    rules:
    - apiGroups: [""]
      resources: ["configmaps"]
      verbs: ["get"]
    '
    ```
---

#### 1.5 更新角色

**请求**
   `PUT /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/roles/{name}`

   更新一个已存在的角色。请求体中需要包含完整的角色定义。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id`| string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色所在的命名空间 |
    | `name` | string | 是 | 要更新的角色名称 |

*   **Body 参数**: 完整的 Kubernetes Role 资源对象。

**示例**

*   **Curl 调用**
    ```shell
    curl -X PUT 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/namespaces/default/roles/my-role' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "apiVersion": "rbac.authorization.k8s.io/v1",
        "kind": "Role",
        "metadata": {
            "name": "my-role",
            "namespace": "default"
        },
        "rules": [
            {
                "apiGroups": [""],
                "resources": ["pods", "pods/log"],
                "verbs": ["get", "list"]
            }
        ]
    }'
    ```

---

#### 1.6 删除角色

**请求**
   `DELETE /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/roles/{name}`

   删除一个角色。

**业务逻辑**
   在执行删除操作前，API服务必须检查该 `Role` 是否被任何 `RoleBinding` 引用。
   - 如果存在关联的 `RoleBinding`，则应中止删除，并返回 `409 Conflict` 错误。
   - 如果没有关联的 `RoleBinding`，则正常执行删除。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id`| string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色所在的命名空间 |
    | `name` | string | 是 | 要删除的角色名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": "Role 'my-role' deleted successfully."
    }
    ```
*   **错误响应 (409 Conflict)**
    ```json
    {
        "code": 40901,
        "success": false,
        "message": "Cannot delete role 'my-role' because it is still in use by one or more RoleBindings (e.g., 'my-role-binding'). Please remove the bindings first."
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X DELETE 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/namespaces/default/roles/my-role' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 1.7 校验角色名称

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/roles/validate`

   校验指定的角色名称在命名空间内是否已存在。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id`| string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色所在的命名空间 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `name` | string | 是 | 需要校验的角色名称 |

**出参**

*   **成功响应 - 名称可用 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": "Role name 'new-role' is available."
    }
    ```
*   **错误响应 - 名称已存在 (409 Conflict)**
    ```json
    {
        "code": 40902,
        "success": false,
        "message": "Role name 'existing-role' already exists in namespace 'default'."
    }
    ```

**示例**

*   **Curl 调用 (校验可用名称)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/namespaces/default/roles/validate?name=new-role' \
    --header 'Authorization: Bearer <token>'
    ```
*   **Curl 调用 (校验已存在名称)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/namespaces/default/roles/validate?name=existing-role' \
    --header 'Authorization: Bearer <token>'
    ```

---
### 2. 集群角色管理 (ClusterRole)

`ClusterRole` 是集群级别的资源，其权限在整个集群范围内有效。

#### 2.1 获取集群角色列表

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/clusterroles`

   获取指定集群内的集群角色列表。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `selector` | string | 否 | 符合查询参数的选择器。例如：`name=a`；模糊查询 `name~a`。 |
    | `page_num` | integer | 否 | 页码, 默认为1。如果 `page_num` 和 `page_size` 均不提供，则返回所有对象。 |
    | `page_size` | integer | 否 | 每页大小, 默认为10。 |
    | `sort_func` | string | 否 | 排序字段比较方式，例如 `time`, `string`, `number`。 |
    | `sort_name` | string | 否 | 排序字段，通过类似 JSONPath 的方式获取, 默认为 `.metadata.name`。 |
    | `sort_order` | string | 否 | 排序方式 `asc` 或 `desc`，默认 `desc`。 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "total": 1,
            "items": [
                {
                    "metadata": {
                        "name": "cluster-admin",
                        "uid": "b1c2d3e4-f5g6-h7i8-j9k0-l1m2n3o4p5q6",
                        "creationTimestamp": "2025-06-12T12:00:00Z"
                    },
                    "rules": [
                        {
                            "verbs": ["*"],
                            "apiGroups": ["*"],
                            "resources": ["*"]
                        }
                    ]
                }
            ]
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterroles?selector=name~admin&page_num=1&page_size=5' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 2.2 获取集群角色详情

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/clusterroles/{name}`

   获取单个集群角色的详细信息。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `name` | string | 是 | 集群角色的名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": {
                "name": "cluster-admin",
                "uid": "b1c2d3e4-f5g6-h7i8-j9k0-l1m2n3o4p5q6",
                "creationTimestamp": "2025-06-12T12:00:00Z"
            },
            "rules": [
                {
                    "verbs": ["*"],
                    "apiGroups": ["*"],
                    "resources": ["*"]
                }
            ],
            "aggregationRule": {
                "clusterRoleSelectors": [
                    {
                        "matchLabels": {
                            "rbac.example.com/aggregate-to-admin": "true"
                        }
                    }
                ]
            }
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterroles/cluster-admin' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 2.3 创建集群角色 (表单方式)

**请求**
   `POST /olympus-portal/apis/v1/clusters/{cluster_id}/clusterroles`

   创建一个新集群角色。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |

*   **Body 参数**
    ```json
    {
        "name": "new-cluster-role",
        "labels": {
            "creator": "admin"
        },
        "rules": [
            {
                "verbs": ["get", "list"],
                "apiGroups": ["apps"],
                "resources": ["deployments", "statefulsets"]
            }
        ]
    }
    ```

**出参**

*   **成功响应 (201 Created)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": {
                "name": "new-cluster-role",
                "creationTimestamp": "2025-06-12T13:00:00Z"
            },
            "rules": [
                {
                    "verbs": ["get", "list"],
                    "apiGroups": ["apps"],
                    "resources": ["deployments", "statefulsets"]
                }
            ]
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterroles' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "new-cluster-role",
        "labels": { "creator": "admin" },
        "rules": [
            { "verbs": ["get", "list"], "apiGroups": ["apps"], "resources": ["deployments", "statefulsets"] }
        ]
    }'
    ```

---

#### 2.4 通过YAML操作集群角色

**说明**

根据要求，通过YAML创建、更新或校验资源时，应直接调用平台提供的K8s代理接口。

*   **创建**: 使用 `POST` 方法。
*   **更新**: 使用 `PUT` 方法。
*   **校验**: 在创建或更新请求的URL后附加 `?dryRun=All` 参数。API服务器会执行除持久化外的所有检查。

**请求**

*   **创建**: `POST /olympus-portal/k8s/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/clusterroles`
*   **更新**: `PUT /olympus-portal/k8s/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/clusterroles/{name}`
*   **校验**: `POST /olympus-portal/k8s/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/clusterroles?dryRun=All`

**入参**

*   **Body 参数**: 完整的 Kubernetes ClusterRole 资源对象 (JSON/YAML格式)。

**示例**

*   **Curl 调用 (校验YAML)**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/k8s/clusters/my-cluster/apis/rbac.authorization.k8s.io/v1/clusterroles?dryRun=All' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/yaml' \
    --data-raw '
    apiVersion: rbac.authorization.k8s.io/v1
    kind: ClusterRole
    metadata:
      name: yaml-validated-cluster-role
    rules:
    - apiGroups: [""]
      resources: ["nodes"]
      verbs: ["get", "list", "watch"]
    '
    ```
---

#### 2.5 更新集群角色

**请求**
   `PUT /olympus-portal/apis/v1/clusters/{cluster_id}/clusterroles/{name}`

   更新一个已存在的集群角色。请求体中需要包含完整的集群角色定义。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id`| string | 是 | 集群的唯一标识 |
    | `name` | string | 是 | 要更新的集群角色名称 |

*   **Body 参数**: 完整的 Kubernetes ClusterRole 资源对象。

**示例**

*   **Curl 调用**
    ```shell
    curl -X PUT 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterroles/my-cluster-role' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "apiVersion": "rbac.authorization.k8s.io/v1",
        "kind": "ClusterRole",
        "metadata": {
            "name": "my-cluster-role"
        },
        "rules": [
            {
                "apiGroups": [""],
                "resources": ["persistentvolumes"],
                "verbs": ["get", "list", "create", "delete"]
            }
        ]
    }'
    ```

---

#### 2.6 删除集群角色

**请求**
   `DELETE /olympus-portal/apis/v1/clusters/{cluster_id}/clusterroles/{name}`

   删除一个集群角色。

**业务逻辑**
   在执行删除操作前，API服务必须检查该 `ClusterRole` 是否被任何 `ClusterRoleBinding` 或 `RoleBinding` 引用。
   - 如果存在关联的绑定，则应中止删除，并返回 `409 Conflict` 错误。
   - 如果没有关联的绑定，则正常执行删除。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id`| string | 是 | 集群的唯一标识 |
    | `name` | string | 是 | 要删除的集群角色名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": "ClusterRole 'my-cluster-role' deleted successfully."
    }
    ```
*   **错误响应 (409 Conflict)**
    ```json
    {
        "code": 40901,
        "success": false,
        "message": "Cannot delete ClusterRole 'my-cluster-role' because it is still in use by one or more bindings (e.g., 'my-cluster-role-binding'). Please remove the bindings first."
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X DELETE 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterroles/my-cluster-role' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 2.7 校验集群角色名称

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/clusterroles/validate`

   校验指定的集群角色名称是否已存在。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id`| string | 是 | 集群的唯一标识 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `name` | string | 是 | 需要校验的集群角色名称 |

**出参**

*   **成功响应 - 名称可用 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": "ClusterRole name 'new-cr' is available."
    }
    ```
*   **错误响应 - 名称已存在 (409 Conflict)**
    ```json
    {
        "code": 40902,
        "success": false,
        "message": "ClusterRole name 'existing-cr' already exists."
    }
    ```

**示例**

*   **Curl 调用 (校验可用名称)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterroles/validate?name=new-cr' \
    --header 'Authorization: Bearer <token>'
    ```
*   **Curl 调用 (校验已存在名称)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterroles/validate?name=existing-cr' \
    --header 'Authorization: Bearer <token>'
    ```

---

### 3. 获取集群可用API资源

**说明**

此API用于发现在集群中所有可用的API资源，为权限配置等UI界面提供数据支持。

#### 3.1 获取可用资源列表

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/resources`

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id`| string | 是 | 集群的唯一标识 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `apiGroup` | string | 否 | 如果提供，则只返回指定API Group下的资源。 |
    | `keyword` | string | 否 | 用于模糊搜索资源名称 (`name` 字段)。 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "verbs": [ "get", "list", "watch", "create", "update", "patch", "delete", "deletecollection" ],
            "resources": [
                {
                    "group": "core",
                    "version": "v1",
                    "resources": [
                        { "name": "pods", "namespaced": true, "kind": "Pod" },
                        { "name": "services", "namespaced": true, "kind": "Service" },
                        { "name": "configmaps", "namespaced": true, "kind": "ConfigMap" }
                    ]
                },
                {
                    "group": "apps",
                    "version": "v1",
                    "resources": [
                        { "name": "deployments", "namespaced": true, "kind": "Deployment" },
                        { "name": "statefulsets", "namespaced": true, "kind": "StatefulSet" }
                    ]
                },
                {
                    "group": "batch",
                    "version": "v1",
                    "resources": [
                        { "name": "jobs", "namespaced": true, "kind": "Job" },
                        { "name": "cronjobs", "namespaced": true, "kind": "CronJob" }
                    ]
                }
            ]
        }
    }
    ```

**示例**

*   **Curl 调用 (获取所有资源)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/resources' \
    --header 'Authorization: Bearer <token>'
    ```
*   **Curl 调用 (按apiGroup筛选)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/resources?apiGroup=apps' \
    --header 'Authorization: Bearer <token>'
    ```

---

### 4. 主体 (Subject) 数据结构

**说明**

为了在API层面更好地反映业务实体，并简化前端处理逻辑，所有`RoleBinding`和`ClusterRoleBinding`的`subjects`字段将使用以下结构。后端负责在业务模型和Kubernetes原生`Subject`格式之间进行转换。

**API输入模型 (用于POST/PUT请求)**

`subjects` 字段为一个数组，每个元素包含：

| 字段 | 类型 | 是否必需 | 描述 |
| --- | --- | --- | --- |
| `kind` | string | 是 | 主体的业务类型。可选值：`PlatformUser`, `PlatformRole`, `Tenant`, `Project`, `ServiceAccount`, `KubernetesUser`, `KubernetesGroup` |
| `id` | string | 是 | 平台实体的唯一ID（当`kind`为平台实体时） |
| `name` | string | 是 | Kubernetes原生实体的名称（当`kind`为`ServiceAccount`, `KubernetesUser`, `KubernetesGroup`时） |
| `namespace` | string | 否 | 仅当`kind`为`ServiceAccount`时需要，表示服务账号所在的命名空间。 |

**API输出模型 (用于GET响应)**

`subjects` 字段为一个数组，每个元素包含：

| 字段 | 类型 | 描述 |
| --- | --- | --- |
| `kind` | string | 主体的业务类型，同输入模型。 |
| `id` | string | 平台实体的唯一ID（如果适用）。 |
| `name` | string | 实体的显示名称（例如用户姓名、项目名）。对于原生K8s实体，则是其资源名。 |
| `namespace` | string | `ServiceAccount`所在的命名空间。 |

---

### 5. 角色绑定管理 (RoleBinding)

`RoleBinding` 将一个 `Role` 中定义的权限授予一个或多个主体（用户、用户组或服务账号），此绑定在特定命名空间内生效。

#### 5.1 获取角色绑定列表

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/rolebindings`

   获取指定命名空间内的角色绑定列表。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色绑定所在的命名空间 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `selector` | string | 否 | 符合查询参数的选择器。 |
    | `page_num` | integer | 否 | 页码, 默认为1。 |
    | `page_size` | integer | 否 | 每页大小, 默认为10。 |
    | `sort_name` | string | 否 | 排序字段, 默认为 `.metadata.name`。 |
    | `sort_order` | string | 否 | 排序方式 `asc` 或 `desc`，默认 `desc`。 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "total": 1,
            "items": [
                {
                    "metadata": {
                        "name": "my-role-binding",
                        "namespace": "default",
                        "creationTimestamp": "2025-06-12T14:00:00Z"
                    },
                    "subjects": [
                        { "kind": "PlatformUser", "id": "jane.doe", "name": "Jane Doe" },
                        { "kind": "ServiceAccount", "name": "default", "namespace": "default" }
                    ],
                    "roleRef": { "kind": "Role", "name": "pod-reader", "apiGroup": "rbac.authorization.k8s.io" }
                }
            ]
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/namespaces/default/rolebindings' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 5.2 获取角色绑定详情

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/rolebindings/{name}`

   获取单个角色绑定的详细信息。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色绑定所在的命名空间 |
    | `name` | string | 是 | 角色绑定的名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": {
                "name": "my-role-binding",
                "namespace": "default",
                "creationTimestamp": "2025-06-12T14:00:00Z"
            },
            "subjects": [
                { "kind": "PlatformUser", "id": "jane.doe", "name": "Jane Doe" }
            ],
            "roleRef": { "kind": "Role", "name": "pod-reader", "apiGroup": "rbac.authorization.k8s.io" }
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/namespaces/default/rolebindings/my-role-binding' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 5.3 创建角色绑定 (表单方式)

**请求**
   `POST /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/rolebindings`

   创建一个新角色绑定。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色绑定将要创建于的命名空间 |

*   **Body 参数**
    ```json
    {
        "name": "new-rb-from-form",
        "roleName": "pod-reader",
        "roleKind": "Role",
        "subjects": [
            { "kind": "PlatformUser", "id": "john.smith" },
            { "kind": "Project", "id": "project-a" },
            { "kind": "ServiceAccount", "name": "build-robot", "namespace": "default" }
        ]
    }
    ```
    *注: `roleKind` 可以是 `Role` 或 `ClusterRole`。当引用 `ClusterRole` 时，`RoleBinding` 仍然只在指定命名空间内生效。*

**出参**

*   **成功响应 (201 Created)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": { "name": "new-rb-from-form" }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/namespaces/default/rolebindings' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "new-rb-from-form",
        "roleName": "pod-reader",
        "roleKind": "Role",
        "subjects": [
            { "kind": "PlatformUser", "id": "john.smith" }
        ]
    }'
    ```

---

#### 5.4 通过YAML操作角色绑定

**说明**

通过YAML创建、更新或校验资源时，应直接调用平台提供的K8s代理接口。

*   **创建**: 使用 `POST` 方法。
*   **更新**: 使用 `PUT` 方法。
*   **校验**: 在创建或更新请求的URL后附加 `?dryRun=All` 参数。

**请求**

*   **创建**: `POST /olympus-portal/k8s/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/namespaces/{namespace}/rolebindings`
*   **更新**: `PUT /olympus-portal/k8s/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/namespaces/{namespace}/rolebindings/{name}`
*   **校验**: `POST ...?dryRun=All`

**入参**

*   **Body 参数**: 完整的 Kubernetes RoleBinding 资源对象 (JSON/YAML格式)。

**示例**

*   **Curl 调用 (校验YAML)**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/k8s/clusters/my-cluster/apis/rbac.authorization.k8s.io/v1/namespaces/default/rolebindings?dryRun=All' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/yaml' \
    --data-raw '
    apiVersion: rbac.authorization.k8s.io/v1
    kind: RoleBinding
    metadata:
      name: yaml-validated-rb
      namespace: default
    subjects:
    - kind: User
      name: jane.doe
      apiGroup: rbac.authorization.k8s.io
    roleRef:
      kind: Role
      name: pod-reader
      apiGroup: rbac.authorization.k8s.io
    '
    ```

---

#### 5.5 更新角色绑定

**请求**
   `PUT /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/rolebindings/{name}`

   更新一个已存在的角色绑定。此接口通常用于更新绑定的主体（Subjects）。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id`| string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色绑定所在的命名空间 |
    | `name` | string | 是 | 要更新的角色绑定名称 |

*   **Body 参数**
    ```json
    {
        "roleName": "pod-reader",
        "roleKind": "Role",
        "subjects": [
            { "kind": "PlatformUser", "id": "peter.jones" }
        ]
    }
    ```
    *注: `roleKind` 可以是 `Role` 或 `ClusterRole`。当引用 `ClusterRole` 时，`RoleBinding` 仍然只在指定命名空间内生效。*

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": { "name": "my-role-binding" }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X PUT 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/namespaces/default/rolebindings/my-role-binding' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "roleName": "pod-reader",
        "roleKind": "Role",
        "subjects": [
            { "kind": "PlatformUser", "id": "peter.jones" }
        ]
    }'
    ```

---

#### 5.6 删除角色绑定

**请求**
   `DELETE /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/rolebindings/{name}`

   删除一个角色绑定。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id`| string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色绑定所在的命名空间 |
    | `name` | string | 是 | 要删除的角色绑定名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": "RoleBinding 'my-role-binding' deleted successfully."
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X DELETE 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/namespaces/default/rolebindings/my-role-binding' \
    --header 'Authorization: Bearer <token>'
    ```

---

### 6. 集群角色绑定管理 (ClusterRoleBinding)

`ClusterRoleBinding` 将一个 `ClusterRole` 中定义的权限授予一个或多个主体（用户、用户组或服务账号），此绑定在整个集群范围内生效。

#### 6.1 获取集群角色绑定列表

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/clusterrolebindings`

   获取指定集群内的集群角色绑定列表。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `selector` | string | 否 | 符合查询参数的选择器。 |
    | `page_num` | integer | 否 | 页码, 默认为1。 |
    | `page_size` | integer | 否 | 每页大小, 默认为10。 |
    | `sort_name` | string | 否 | 排序字段, 默认为 `.metadata.name`。 |
    | `sort_order` | string | 否 | 排序方式 `asc` 或 `desc`，默认 `desc`。 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "total": 1,
            "items": [
                {
                    "metadata": {
                        "name": "my-cluster-role-binding",
                        "creationTimestamp": "2025-06-12T15:00:00Z"
                    },
                    "subjects": [
                        { "kind": "PlatformRole", "id": "admin", "name": "平台管理员" },
                        { "kind": "Tenant", "id": "finance-dept", "name": "财务部" }
                    ],
                    "roleRef": { "kind": "ClusterRole", "name": "cluster-admin", "apiGroup": "rbac.authorization.k8s.io" }
                }
            ]
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterrolebindings' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 6.2 获取集群角色绑定详情

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/clusterrolebindings/{name}`

   获取单个集群角色绑定的详细信息。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `name` | string | 是 | 集群角色绑定的名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": {
                "name": "my-cluster-role-binding",
                "creationTimestamp": "2025-06-12T15:00:00Z"
            },
            "subjects": [
                { "kind": "PlatformRole", "id": "admin", "name": "平台管理员" }
            ],
            "roleRef": { "kind": "ClusterRole", "name": "cluster-admin", "apiGroup": "rbac.authorization.k8s.io" }
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterrolebindings/my-cluster-role-binding' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 6.3 创建集群角色绑定 (表单方式)

**请求**
   `POST /olympus-portal/apis/v1/clusters/{cluster_id}/clusterrolebindings`

   创建一个新集群角色绑定。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |

*   **Body 参数**
    ```json
    {
        "name": "new-crb-from-form",
        "roleName": "cluster-admin",
        "subjects": [
            { "kind": "PlatformUser", "id": "john.smith" },
            { "kind": "Tenant", "id": "finance-dept" }
        ]
    }
    ```

**出参**

*   **成功响应 (201 Created)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": { "name": "new-crb-from-form" }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterrolebindings' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "new-crb-from-form",
        "roleName": "cluster-admin",
        "subjects": [
            { "kind": "PlatformUser", "id": "john.smith" }
        ]
    }'
    ```

---

#### 6.4 通过YAML操作集群角色绑定

**说明**

通过YAML创建、更新或校验资源时，应直接调用平台提供的K8s代理接口。

*   **创建**: 使用 `POST` 方法。
*   **更新**: 使用 `PUT` 方法。
*   **校验**: 在创建或更新请求的URL后附加 `?dryRun=All` 参数。

**请求**

*   **创建**: `POST /olympus-portal/k8s/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/clusterrolebindings`
*   **更新**: `PUT /olympus-portal/k8s/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/clusterrolebindings/{name}`
*   **校验**: `POST ...?dryRun=All`

**入参**

*   **Body 参数**: 完整的 Kubernetes ClusterRoleBinding 资源对象 (JSON/YAML格式)。

**示例**

*   **Curl 调用 (校验YAML)**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/k8s/clusters/my-cluster/apis/rbac.authorization.k8s.io/v1/clusterrolebindings?dryRun=All' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/yaml' \
    --data-raw '
    apiVersion: rbac.authorization.k8s.io/v1
    kind: ClusterRoleBinding
    metadata:
      name: yaml-validated-crb
    subjects:
    - kind: User
      name: jane.doe
      apiGroup: rbac.authorization.k8s.io
    roleRef:
      kind: ClusterRole
      name: cluster-admin
      apiGroup: rbac.authorization.k8s.io
    '
    ```

---

#### 6.5 更新集群角色绑定

**请求**
   `PUT /olympus-portal/apis/v1/clusters/{cluster_id}/clusterrolebindings/{name}`

   更新一个已存在的集群角色绑定。此接口通常用于更新绑定的主体（Subjects）。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id`| string | 是 | 集群的唯一标识 |
    | `name` | string | 是 | 要更新的集群角色绑定名称 |

*   **Body 参数**
    ```json
    {
        "roleName": "cluster-admin",
        "subjects": [
            { "kind": "PlatformUser", "id": "peter.jones" }
        ]
    }
    ```

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": { "name": "my-cluster-role-binding" }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X PUT 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterrolebindings/my-cluster-role-binding' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "roleName": "cluster-admin",
        "subjects": [
            { "kind": "PlatformUser", "id": "peter.jones" }
        ]
    }'
    ```

---

#### 6.6 删除集群角色绑定

**请求**
   `DELETE /olympus-portal/apis/v1/clusters/{cluster_id}/clusterrolebindings/{name}`

   删除一个集群角色绑定。

**业务逻辑**
   在执行删除操作前，API服务会检查该 `ClusterRoleBinding` 是否带有系统保护标签（例如 `rbac.olympus.io/system-protected: "true"`）。
   - 如果带有该标签，则应中止删除，并返回 `403 Forbidden` 错误。
   - 否则，正常执行删除。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id`| string | 是 | 集群的唯一标识 |
    | `name` | string | 是 | 要删除的集群角色绑定名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": "ClusterRoleBinding 'my-cluster-role-binding' deleted successfully."
    }
    ```
*   **错误响应 (403 Forbidden)**
    ```json
    {
        "code": 40301,
        "success": false,
        "message": "Cannot delete system-protected resource 'my-cluster-role-binding'."
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X DELETE 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterrolebindings/my-cluster-role-binding' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 6.7 校验集群角色绑定

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/clusterrolebindings/validate`

   校验指定的集群角色绑定名称是否已存在。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id`| string | 是 | 集群的唯一标识 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `name` | string | 是 | 需要校验的集群角色绑定名称 |

**出参**

*   **成功响应 - 名称可用 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": "ClusterRoleBinding name 'new-crb' is available."
    }
    ```
*   **错误响应 - 名称已存在 (409 Conflict)**
    ```json
    {
        "code": 40902,
        "success": false,
        "message": "ClusterRoleBinding name 'existing-crb' already exists."
    }
    ```

**示例**

*   **Curl 调用 (校验可用名称)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterrolebindings/validate?name=new-crb' \
    --header 'Authorization: Bearer <token>'
    ```
*   **Curl 调用 (校验已存在名称)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/clusterrolebindings/validate?name=existing-crb' \
    --header 'Authorization: Bearer <token>'
    ```

---

### 7. 服务账号管理 (ServiceAccount)

#### 7.1 获取服务账号列表

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/serviceaccounts`

   获取指定集群内的服务账号列表。可以按命名空间进行过滤。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `namespace` | string | 否 | 命名空间名称。如果未提供，则查询所有命名空间下的服务账号。 |
    | `selector` | string | 否 | 符合查询参数的选择器。例如：`name=a,namespace=default`；模糊查询 `name~a`。 |
    | `page_num` | integer | 否 | 页码, 默认为1。 |
    | `page_size` | integer | 否 | 每页大小, 默认为10。 |
    | `sort_func` | string | 否 | 排序字段比较方式，例如 `time`, `string`, `number`。 |
    | `sort_name` | string | 否 | 排序字段，通过类似 JSONPath 的方式获取, 默认为 `.metadata.name`。 |
    | `sort_order` | string | 否 | 排序方式 `asc` 或 `desc`，默认 `desc`。 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "total": 2,
            "items": [
                {
                    "metadata": {
                        "name": "default",
                        "namespace": "default",
                        "uid": "a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6",
                        "creationTimestamp": "2025-06-12T10:00:00Z"
                    },
                    "secrets": [
                        { "name": "default-token-xyz" }
                    ],
                    "protected": true
                },
                {
                    "metadata": {
                        "name": "my-sa",
                        "namespace": "default",
                        "uid": "b2c3d4e5-f6a7-b8c9-d0e1-f2a3b4c5d6e7",
                        "creationTimestamp": "2025-06-12T11:00:00Z"
                    },
                    "secrets": [],
                    "protected": false
                }
            ]
        }
    }
    ```
    *   **`protected`**: 布尔值，`true` 表示该服务账号受系统保护，不允许删除。

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/clusters/my-cluster/serviceaccounts?namespace=default' \
    --header 'Authorization: Bearer <token>'
    ```
---

#### 7.2 获取服务账号详情

**请求**
   `GET /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/serviceaccounts/{name}`

   获取特定命名空间下单个服务账号的详细信息。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 服务账号所在的命名空间 |
    | `name` | string | 是 | 服务账号的名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": {
                "name": "default",
                "namespace": "default",
                "uid": "a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6",
                "creationTimestamp": "2025-06-12T10:00:00Z",
                "labels": { "app": "my-app" },
                "annotations": { "description": "Default service account" }
            },
            "secrets": [
                { "name": "default-token-xyz" }
            ],
            "protected": true
        }
    }
    ```

---

#### 7.3 创建服务账号 (表单方式)

**请求**
   `POST /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/serviceaccounts`

   在指定命名空间中创建一个新服务账号。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 服务账号将要创建于的命名空间 |

*   **Body 参数**
    ```json
    {
        "name": "new-sa",
        "labels": { "creator": "admin" },
        "annotations": { "managed-by": "olympus" }
    }
    ```

**出参**

*   **成功响应 (201 Created)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": { "name": "new-sa", "namespace": "default" }
        }
    }
    ```

---

#### 7.4 通过YAML操作服务账号

**说明**
   通过YAML创建或更新服务账号，应直接调用平台提供的K8s代理接口。

**请求**
*   **创建**: `POST /olympus-portal/k8s/clusters/{cluster_id}/api/v1/namespaces/{namespace}/serviceaccounts`
*   **更新**: `PUT /olympus-portal/k8s/clusters/{cluster_id}/api/v1/namespaces/{namespace}/serviceaccounts/{name}`

**示例**

*   **Curl 调用 (创建YAML)**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/k8s/clusters/my-cluster/api/v1/namespaces/default/serviceaccounts' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/yaml' \
    --data-raw '
    apiVersion: v1
    kind: ServiceAccount
    metadata:
      name: yaml-created-sa
      namespace: default
    '
    ```
---

#### 7.5 更新服务账号

**请求**
   `PUT /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/serviceaccounts/{name}`

   更新服务账号的标签和注解。

**入参**
*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 服务账号所在的命名空间 |
    | `name` | string | 是 | 服务账号的名称 |

*   **Body 参数**
    ```json
    {
        "labels": { "creator": "admin", "updated": "true" },
        "annotations": { "description": "Updated description" }
    }
    ```

**出参**
*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
             "metadata": { "name": "my-sa", "namespace": "default" }
        }
    }
    ```
---

#### 7.6 删除服务账号

**请求**
   `DELETE /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/serviceaccounts/{name}`

**业务逻辑**

   此端点包含保护逻辑，防止删除关键的服务账号。
   1.  **检查命名空间**：如果服务账号位于 `kube-system`, `kube-public`, `kube-node-lease` 等系统命名空间，则禁止删除。
   2.  **检查名称**：如果服务账号的名称是 `default`，则禁止删除。
   3.  **检查自定义保护标签**：如果服务账号包含 `olympus.io/protected: "true"` 标签，则禁止删除。

**出参**
*   **成功响应 (204 No Content)**

*   **失败响应 (409 Conflict)**
   当尝试删除一个受保护的服务账号时返回。
    ```json
    {
        "code": 409,
        "success": false,
        "message": "ServiceAccount 'default' in namespace 'default' is protected and cannot be deleted."
    }
    ```
---

#### 7.7 校验服务账号名称

**请求**
   `POST /olympus-portal/apis/v1/clusters/{cluster_id}/namespaces/{namespace}/serviceaccounts/validate`

**说明**
   在用户创建服务账号之前，前端可以调用此接口来检查名称是否已存在，以提供即时反馈。

**入参**
*   **Body 参数**
    ```json
    {
        "name": "new-sa-to-validate"
    }
    ```

**出参**
*   **成功响应 (200 OK - 名称可用)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "valid": true
        }
    }
    ```
*   **失败响应 (200 OK - 名称冲突)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "valid": false,
            "reason": "ServiceAccount 'new-sa-to-validate' already exists in namespace 'default'."
        }
    }
    ```
---


## 第二部分：工作空间API

### 1. 角色管理 (Role)

#### 1.1 获取角色列表

**请求**
   `GET /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/roles`

   获取指定项目内的角色列表。可以按命名空间进行过滤。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `namespace` | string | 否 | 命名空间名称。如果未提供，则查询所有命名空间下的角色。 |
    | `selector` | string | 否 | 符合查询参数的选择器。例如：`name=a,namespace=default`；模糊查询 `name~a`。 |
    | `page_num` | integer | 否 | 页码, 默认为1。如果 `page_num` 和 `page_size` 均不提供，则返回所有对象。 |
    | `page_size` | integer | 否 | 每页大小, 默认为10。 |
    | `sort_func` | string | 否 | 排序字段比较方式，例如 `time`, `string`, `number`。 |
    | `sort_name` | string | 否 | 排序字段，通过类似 JSONPath 的方式获取, 默认为 `.metadata.name`。 |
    | `sort_order` | string | 否 | 排序方式 `asc` 或 `desc`，默认 `desc`。 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "total": 1,
            "items": [
                {
                    "metadata": {
                        "name": "my-role",
                        "namespace": "default",
                        "uid": "a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6",
                        "creationTimestamp": "2025-06-12T10:00:00Z"
                    },
                    "rules": [
                        {
                            "verbs": ["get", "list", "watch"],
                            "apiGroups": [""],
                            "resources": ["pods"]
                        }
                    ]
                }
            ]
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/roles?namespace=default&selector=name~my&page_num=1&page_size=10' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 1.2 获取角色详情

**请求**
   `GET /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/roles/{name}`

   获取特定命名空间下单个角色的详细信息。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色所在的命名空间 |
    | `name` | string | 是 | 角色的名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": {
                "name": "my-role",
                "namespace": "default",
                "uid": "a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6",
                "creationTimestamp": "2025-06-12T10:00:00Z",
                "labels": {
                    "app": "my-app"
                }
            },
            "rules": [
                {
                    "verbs": ["get", "list", "watch"],
                    "apiGroups": [""],
                    "resources": ["pods"]
                },
                {
                    "verbs": ["create", "delete"],
                    "apiGroups": ["apps"],
                    "resources": ["deployments"]
                }
            ]
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/roles/my-role' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 1.3 创建角色 (表单方式)

**请求**
   `POST /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/roles`

   在指定命名空间中创建一个新角色。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色将要创建于的命名空间 |

*   **Body 参数**
    ```json
    {
        "name": "new-role",
        "labels": {
            "creator": "admin"
        },
        "rules": [
            {
                "verbs": ["get", "list"],
                "apiGroups": [""],
                "resources": ["services", "endpoints"]
            }
        ]
    }
    ```

**出参**

*   **成功响应 (201 Created)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": {
                "name": "new-role",
                "namespace": "default",
                "creationTimestamp": "2025-06-12T11:00:00Z"
            },
            "rules": [
                {
                    "verbs": ["get", "list"],
                    "apiGroups": [""],
                    "resources": ["services", "endpoints"]
                }
            ]
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/roles' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "new-role",
        "labels": { "creator": "admin" },
        "rules": [
            { "verbs": ["get", "list"], "apiGroups": [""], "resources": ["services", "endpoints"] }
        ]
    }'
    ```

---

#### 1.4 通过YAML操作角色

**说明**

根据要求，通过YAML创建、更新或校验资源时，应直接调用平台提供的K8s代理接口。

*   **创建**: 使用 `POST` 方法。
*   **更新**: 使用 `PUT` 方法。
*   **校验**: 在创建或更新请求的URL后附加 `?dryRun=All` 参数。API服务器会执行除持久化外的所有检查。

**请求**

*   **创建**: `POST /olympus-portal/k8s/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/namespaces/{namespace}/roles`
*   **更新**: `PUT /olympus-portal/k8s/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/namespaces/{namespace}/roles/{name}`
*   **校验**: `POST /olympus-portal/k8s/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/namespaces/{namespace}/roles?dryRun=All`

**入参**

*   **Body 参数**: 完整的 Kubernetes Role 资源对象 (JSON/YAML格式)。

**示例**

*   **Curl 调用 (校验YAML)**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/k8s/organizations/my-org/projects/my-project/clusters/my-cluster/apis/rbac.authorization.k8s.io/v1/namespaces/default/roles?dryRun=All' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/yaml' \
    --data-raw '
    apiVersion: rbac.authorization.k8s.io/v1
    kind: Role
    metadata:
      name: yaml-validated-role
      namespace: default
    rules:
    - apiGroups: [""]
      resources: ["configmaps"]
      verbs: ["get"]
    '
    ```
---

#### 1.5 更新角色

**请求**
   `PUT /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/roles/{name}`

   更新一个已存在的角色。请求体中需要包含完整的角色定义。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色所在的命名空间 |
    | `name` | string | 是 | 要更新的角色名称 |

*   **Body 参数**: 完整的 Kubernetes Role 资源对象。

**示例**

*   **Curl 调用**
    ```shell
    curl -X PUT 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/roles/my-role' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "apiVersion": "rbac.authorization.k8s.io/v1",
        "kind": "Role",
        "metadata": {
            "name": "my-role",
            "namespace": "default"
        },
        "rules": [
            {
                "apiGroups": [""],
                "resources": ["pods", "pods/log"],
                "verbs": ["get", "list"]
            }
        ]
    }'
    ```

---

#### 1.6 删除角色

**请求**
   `DELETE /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/roles/{name}`

   删除一个角色。

**业务逻辑**
   在执行删除操作前，API服务必须检查该 `Role` 是否被任何 `RoleBinding` 引用。
   - 如果存在关联的 `RoleBinding`，则应中止删除，并返回 `409 Conflict` 错误。
   - 如果没有关联的 `RoleBinding`，则正常执行删除。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色所在的命名空间 |
    | `name` | string | 是 | 要删除的角色名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": "Role 'my-role' deleted successfully."
    }
    ```
*   **错误响应 (409 Conflict)**
    ```json
    {
        "code": 40901,
        "success": false,
        "message": "Cannot delete role 'my-role' because it is still in use by one or more RoleBindings (e.g., 'my-role-binding'). Please remove the bindings first."
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X DELETE 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/roles/my-role' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 1.7 校验角色名称

**请求**
   `GET /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/roles/validate`

   校验指定的角色名称在命名空间内是否已存在。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色所在的命名空间 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `name` | string | 是 | 需要校验的角色名称 |

**出参**

*   **成功响应 - 名称可用 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": "Role name 'new-role' is available."
    }
    ```
*   **错误响应 - 名称已存在 (409 Conflict)**
    ```json
    {
        "code": 40902,
        "success": false,
        "message": "Role name 'existing-role' already exists in namespace 'default'."
    }
    ```

**示例**

*   **Curl 调用 (校验可用名称)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/roles/validate?name=new-role' \
    --header 'Authorization: Bearer <token>'
    ```
*   **Curl 调用 (校验已存在名称)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/roles/validate?name=existing-role' \
    --header 'Authorization: Bearer <token>'
    ```

---

### 2. 角色绑定管理 (RoleBinding)

`RoleBinding` 将一个 `Role` 中定义的权限授予一个或多个主体（用户、用户组或服务账号），此绑定在特定命名空间内生效。

#### 2.1 获取角色绑定列表

**请求**
   `GET /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/rolebindings`

   获取指定命名空间内的角色绑定列表。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色绑定所在的命名空间 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `selector` | string | 否 | 符合查询参数的选择器。 |
    | `page_num` | integer | 否 | 页码, 默认为1。 |
    | `page_size` | integer | 否 | 每页大小, 默认为10。 |
    | `sort_name` | string | 否 | 排序字段, 默认为 `.metadata.name`。 |
    | `sort_order` | string | 否 | 排序方式 `asc` 或 `desc`，默认 `desc`。 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "total": 1,
            "items": [
                {
                    "metadata": {
                        "name": "my-role-binding",
                        "namespace": "default",
                        "creationTimestamp": "2025-06-12T14:00:00Z"
                    },
                    "subjects": [
                        { "kind": "PlatformUser", "id": "jane.doe", "name": "Jane Doe" },
                        { "kind": "ServiceAccount", "name": "default", "namespace": "default" }
                    ],
                    "roleRef": { "kind": "Role", "name": "pod-reader", "apiGroup": "rbac.authorization.k8s.io" }
                }
            ]
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/rolebindings' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 2.2 获取角色绑定详情

**请求**
   `GET /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/rolebindings/{name}`

   获取单个角色绑定的详细信息。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色绑定所在的命名空间 |
    | `name` | string | 是 | 角色绑定的名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": {
                "name": "my-role-binding",
                "namespace": "default",
                "creationTimestamp": "2025-06-12T14:00:00Z"
            },
            "subjects": [
                { "kind": "PlatformUser", "id": "jane.doe", "name": "Jane Doe" }
            ],
            "roleRef": { "kind": "Role", "name": "pod-reader", "apiGroup": "rbac.authorization.k8s.io" }
        }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/rolebindings/my-role-binding' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 2.3 创建角色绑定 (表单方式)

**请求**
   `POST /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/rolebindings`

   创建一个新角色绑定。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色绑定将要创建于的命名空间 |

*   **Body 参数**
    ```json
    {
        "name": "new-rb-from-form",
        "roleName": "pod-reader",
        "roleKind": "Role",
        "subjects": [
            { "kind": "PlatformUser", "id": "john.smith" },
            { "kind": "Project", "id": "project-a" },
            { "kind": "ServiceAccount", "name": "build-robot", "namespace": "default" }
        ]
    }
    ```
    *注: `roleKind` 可以是 `Role` 或 `ClusterRole`。当引用 `ClusterRole` 时，`RoleBinding` 仍然只在指定命名空间内生效。*

**出参**

*   **成功响应 (201 Created)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": { "name": "new-rb-from-form" }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/rolebindings' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "new-rb-from-form",
        "roleName": "pod-reader",
        "roleKind": "Role",
        "subjects": [
            { "kind": "PlatformUser", "id": "john.smith" }
        ]
    }'
    ```

---

#### 2.4 通过YAML操作角色绑定

**说明**

通过YAML创建、更新或校验资源时，应直接调用平台提供的K8s代理接口。

*   **创建**: 使用 `POST` 方法。
*   **更新**: 使用 `PUT` 方法。
*   **校验**: 在创建或更新请求的URL后附加 `?dryRun=All` 参数。

**请求**

*   **创建**: `POST /olympus-portal/k8s/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/namespaces/{namespace}/rolebindings`
*   **更新**: `PUT /olympus-portal/k8s/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/apis/rbac.authorization.k8s.io/v1/namespaces/{namespace}/rolebindings/{name}`
*   **校验**: `POST ...?dryRun=All`

**入参**

*   **Body 参数**: 完整的 Kubernetes RoleBinding 资源对象 (JSON/YAML格式)。

**示例**

*   **Curl 调用 (校验YAML)**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/k8s/organizations/my-org/projects/my-project/clusters/my-cluster/apis/rbac.authorization.k8s.io/v1/namespaces/default/rolebindings?dryRun=All' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/yaml' \
    --data-raw '
    apiVersion: rbac.authorization.k8s.io/v1
    kind: RoleBinding
    metadata:
      name: yaml-validated-rb
      namespace: default
    subjects:
    - kind: User
      name: jane.doe
      apiGroup: rbac.authorization.k8s.io
    roleRef:
      kind: Role
      name: pod-reader
      apiGroup: rbac.authorization.k8s.io
    '
    ```

---

#### 2.5 更新角色绑定

**请求**
   `PUT /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/rolebindings/{name}`

   更新一个已存在的角色绑定。此接口通常用于更新绑定的主体（Subjects）。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id`| string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色绑定所在的命名空间 |
    | `name` | string | 是 | 要更新的角色绑定名称 |

*   **Body 参数**
    ```json
    {
        "roleName": "pod-reader",
        "roleKind": "Role",
        "subjects": [
            { "kind": "PlatformUser", "id": "peter.jones" }
        ]
    }
    ```
    *注: `roleKind` 可以是 `Role` 或 `ClusterRole`。当引用 `ClusterRole` 时，`RoleBinding` 仍然只在指定命名空间内生效。*

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": { "name": "my-role-binding" }
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X PUT 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/rolebindings/my-role-binding' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "roleName": "pod-reader",
        "roleKind": "Role",
        "subjects": [
            { "kind": "PlatformUser", "id": "peter.jones" }
        ]
    }'
    ```

---

#### 2.6 删除角色绑定

**请求**
   `DELETE /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/rolebindings/{name}`

   删除一个角色绑定。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id`| string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色绑定所在的命名空间 |
    | `name` | string | 是 | 要删除的角色绑定名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": "RoleBinding 'my-role-binding' deleted successfully."
    }
    ```

**示例**

*   **Curl 调用**
    ```shell
    curl -X DELETE 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/rolebindings/my-role-binding' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 2.7 校验角色绑定名称

**请求**
   `GET /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/rolebindings/validate`

   校验指定的角色绑定名称在命名空间内是否已存在。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id`| string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 角色绑定所在的命名空间 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `name` | string | 是 | 需要校验的角色绑定名称 |

**出参**

*   **成功响应 - 名称可用 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": "RoleBinding name 'new-rb' is available."
    }
    ```
*   **错误响应 - 名称已存在 (409 Conflict)**
    ```json
    {
        "code": 40902,
        "success": false,
        "message": "RoleBinding name 'existing-rb' already exists in namespace 'default'."
    }
    ```

**示例**

*   **Curl 调用 (校验可用名称)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/rolebindings/validate?name=new-rb' \
    --header 'Authorization: Bearer <token>'
    ```
*   **Curl 调用 (校验已存在名称)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/rolebindings/validate?name=existing-rb' \
    --header 'Authorization: Bearer <token>'
    ```

---

#### 2.8 获取可绑定的角色列表

**说明**

此接口为创建或编辑 `RoleBinding` 提供数据支持，返回一个聚合列表，其中包含：
1.  指定命名空间下的所有 `Role`。
2.  集群范围内的所有 `ClusterRole`。
这避免了前端需要多次调用不同接口来获取完整的可绑定角色列表。

**请求**
   `GET /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/bindable-roles`

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id`| string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 当前操作的命名空间 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `keyword` | string | 否 | 用于模糊搜索角色名称的关键字。 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "total": 2,
            "items": [
                {
                    "kind": "Role",
                    "name": "namespace-specific-role",
                    "namespace": "default"
                },
                {
                    "kind": "ClusterRole",
                    "name": "cluster-wide-role",
                    "namespace": ""
                }
            ]
        }
    }
    ```
    *注：返回的列表中，`Role` 会有 `namespace` 字段，而 `ClusterRole` 的 `namespace` 字段为空字符串。*

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/namespaces/default/bindable-roles?keyword=role' \
    --header 'Authorization: Bearer <token>'
    ```

---

### 3. 服务账号管理 (ServiceAccount)

#### 3.1 获取服务账号列表

**请求**
   `GET /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/serviceaccounts`

   获取指定项目内的服务账号列表。可以按命名空间进行过滤。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `namespace` | string | 否 | 命名空间名称。如果未提供，则查询所有命名空间下的服务账号。 |
    | `selector` | string | 否 | 符合查询参数的选择器。例如：`name=a,namespace=default`；模糊查询 `name~a`。 |
    | `page_num` | integer | 否 | 页码, 默认为1。 |
    | `page_size` | integer | 否 | 每页大小, 默认为10。 |
    | `sort_func` | string | 否 | 排序字段比较方式，例如 `time`, `string`, `number`。 |
    | `sort_name` | string | 否 | 排序字段，通过类似 JSONPath 的方式获取, 默认为 `.metadata.name`。 |
    | `sort_order` | string | 否 | 排序方式 `asc` 或 `desc`，默认 `desc`。 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "total": 2,
            "items": [
                {
                    "metadata": {
                        "name": "default",
                        "namespace": "default",
                        "uid": "a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6",
                        "creationTimestamp": "2025-06-12T10:00:00Z"
                    },
                    "secrets": [
                        { "name": "default-token-xyz" }
                    ],
                    "protected": true
                },
                {
                    "metadata": {
                        "name": "my-sa",
                        "namespace": "default",
                        "uid": "b2c3d4e5-f6a7-b8c9-d0e1-f2a3b4c5d6e7",
                        "creationTimestamp": "2025-06-12T11:00:00Z"
                    },
                    "secrets": [],
                    "protected": false
                }
            ]
        }
    }
    ```
    *   **`protected`**: 布尔值，`true` 表示该服务账号受系统保护，不允许删除。

**示例**

*   **Curl 调用**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/organizations/my-org/projects/my-project/clusters/my-cluster/serviceaccounts?namespace=default' \
    --header 'Authorization: Bearer <token>'
    ```
---

#### 3.2 获取服务账号详情

**请求**
   `GET /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/serviceaccounts/{name}`

   获取特定命名空间下单个服务账号的详细信息。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 服务账号所在的命名空间 |
    | `name` | string | 是 | 服务账号的名称 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": {
                "name": "default",
                "namespace": "default",
                "uid": "a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6",
                "creationTimestamp": "2025-06-12T10:00:00Z",
                "labels": { "app": "my-app" },
                "annotations": { "description": "Default service account" }
            },
            "secrets": [
                { "name": "default-token-xyz" }
            ],
            "protected": true
        }
    }
    ```

---

#### 3.3 创建服务账号 (表单方式)

**请求**
   `POST /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/serviceaccounts`

   在指定命名空间中创建一个新服务账号。

**入参**

*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 服务账号将要创建于的命名空间 |

*   **Body 参数**
    ```json
    {
        "name": "new-sa",
        "labels": { "creator": "admin" },
        "annotations": { "managed-by": "olympus" }
    }
    ```

**出参**

*   **成功响应 (201 Created)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "metadata": { "name": "new-sa", "namespace": "default" }
        }
    }
    ```

---

#### 3.4 通过YAML操作服务账号

**说明**
   通过YAML创建或更新服务账号，应直接调用平台提供的K8s代理接口。

**请求**
*   **创建**: `POST /olympus-portal/k8s/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/api/v1/namespaces/{namespace}/serviceaccounts`
*   **更新**: `PUT /olympus-portal/k8s/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/api/v1/namespaces/{namespace}/serviceaccounts/{name}`

**示例**

*   **Curl 调用 (创建YAML)**
    ```shell
    curl -X POST 'http://<host>/olympus-portal/k8s/organizations/my-org/projects/my-project/clusters/my-cluster/api/v1/namespaces/default/serviceaccounts' \
    --header 'Authorization: Bearer <token>' \
    --header 'Content-Type: application/yaml' \
    --data-raw '
    apiVersion: v1
    kind: ServiceAccount
    metadata:
      name: yaml-created-sa
      namespace: default
    '
    ```
---

#### 3.5 更新服务账号

**请求**
   `PUT /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/serviceaccounts/{name}`

   更新服务账号的标签和注解。

**入参**
*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 服务账号所在的命名空间 |
    | `name` | string | 是 | 服务账号的名称 |

*   **Body 参数**
    ```json
    {
        "labels": { "creator": "admin", "updated": "true" },
        "annotations": { "description": "Updated description" }
    }
    ```

**出参**
*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
             "metadata": { "name": "my-sa", "namespace": "default" }
        }
    }
    ```
---

#### 3.6 删除服务账号

**请求**
   `DELETE /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/serviceaccounts/{name}`

**业务逻辑**

   此端点包含保护逻辑，防止删除关键的服务账号。
   1.  **检查命名空间**：如果服务账号位于 `kube-system`, `kube-public`, `kube-node-lease` 等系统命名空间，则禁止删除。
   2.  **检查名称**：如果服务账号的名称是 `default`，则禁止删除。
   3.  **检查自定义保护标签**：如果服务账号包含 `olympus.io/protected: "true"` 标签，则禁止删除。

**出参**
*   **成功响应 (204 No Content)**

*   **失败响应 (409 Conflict)**
   当尝试删除一个受保护的服务账号时返回。
    ```json
    {
        "code": 409,
        "success": false,
        "message": "ServiceAccount 'default' in namespace 'default' is protected and cannot be deleted."
    }
    ```
---

#### 3.7 校验服务账号名称

**请求**
   `POST /olympus-portal/apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/serviceaccounts/validate`

**说明**
   在用户创建服务账号之前，前端可以调用此接口来检查名称是否已存在，以提供即时反馈。

**入参**
*   **Path 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `organization_id` | string | 是 | 组织的唯一标识 |
    | `project_id` | string | 是 | 项目的唯一标识 |
    | `cluster_id` | string | 是 | 集群的唯一标识 |
    | `namespace` | string | 是 | 服务账号将要创建于的命名空间 |
*   **Body 参数**
    ```json
    {
        "name": "new-sa-to-validate"
    }
    ```

**出参**
*   **成功响应 (200 OK - 名称可用)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "valid": true
        }
    }
    ```
*   **失败响应 (200 OK - 名称冲突)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "valid": false,
            "reason": "ServiceAccount 'new-sa-to-validate' already exists in namespace 'default'."
        }
    }
    ```
---



---

## 第三部分：平台通用API

### 1. 主体(Subject)查询

**说明**

此接口为平台提供一个统一的查询入口，用于在创建角色绑定（RoleBinding/ClusterRoleBinding）时，搜索和选择需要授权的主体。支持按类型、关键字、项目等多种条件进行组合查询。

**请求**
   `GET /olympus-portal/apis/v1/rbac/subjects`

**入参**

*   **Query 参数**
    | 名称 | 类型 | 是否必需 | 描述 |
    | --- | --- | --- | --- |
    | `kind` | string | 否 | 主体的业务类型。可多选，用逗号分隔。可选值：`PlatformUser`, `PlatformRole`, `Tenant`, `Project`, `ServiceAccount`。 |
    | `keyword` | string | 否 | 用于模糊搜索主体名称或ID的关键字。 |
    | `organization_id` | string | 否 | 当查询 `Project` 或 `PlatformUser` 等组织内资源时，限定在此组织ID范围内。 |
    | `project_id` | string | 否 | 当查询 `ServiceAccount` 等项目内资源时，限定在此项目ID范围内。 |
    | `cluster_id` | string | 否 | 当 `kind` 包含 `ServiceAccount` 时，必须提供此参数以指定集群。 |
    | `namespace` | string | 否 | 当 `kind` 包含 `ServiceAccount` 时，可选，用于限定服务账号的命名空间。 |
    | `page_num` | integer | 否 | 页码, 默认为1。 |
    | `page_size` | integer | 否 | 每页大小, 默认为10。 |

**出参**

*   **成功响应 (200 OK)**
    ```json
    {
        "code": 0,
        "success": true,
        "data": {
            "total": 2,
            "items": [
                {
                    "kind": "PlatformUser",
                    "id": "john.smith",
                    "name": "John Smith",
                    "description": "研发工程师"
                },
                {
                    "kind": "Project",
                    "id": "project-a",
                    "name": "项目A",
                    "description": "核心业务项目"
                }
            ]
        }
    }
    ```
    *   **`id`**: 在`kind`为`ServiceAccount`时，此字段值为其`name`。
    *   **`description`**: 对于不同类型的主体，返回合适的描述信息，如用户职位、项目描述等。

**示例**

*   **Curl 调用 (查询所有用户和项目)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/subjects?kind=PlatformUser,Project&keyword=dev' \
    --header 'Authorization: Bearer <token>'
    ```
*   **Curl 调用 (查询指定项目下的服务账号)**
    ```shell
    curl -X GET 'http://<host>/olympus-portal/apis/v1/subjects?kind=ServiceAccount&project_id=my-project&cluster_id=my-cluster&namespace=default' \
    --header 'Authorization: Bearer <token>'
    ```

---